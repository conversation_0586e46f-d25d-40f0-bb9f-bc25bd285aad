"""
URL configuration for PDFLEX project.
Universal Form-to-PDF Generator
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('apps.core.urls')),
    path('forms/', include('apps.forms.urls')),
    path('pdf/', include('apps.pdf_generators.urls')),
    path('pdf-generation/', include('apps.pdf_generation.urls')),
    path('code-generator/', include('apps.code_generator.urls')),
    path('webhooks/', include('apps.webhooks.urls')),
    path('users/', include('apps.users.urls')),
    path('api/v1/', include('apps.api.urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)

    # Add debug toolbar URLs in development
    if 'debug_toolbar' in settings.INSTALLED_APPS:
        import debug_toolbar
        urlpatterns = [
            path('__debug__/', include(debug_toolbar.urls)),
        ] + urlpatterns
