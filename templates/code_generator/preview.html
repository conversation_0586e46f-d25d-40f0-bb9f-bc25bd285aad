{% extends 'base.html' %}

{% block title %}Code Preview - PDFlex{% endblock %}

{% block content %}
<div class="h-screen flex flex-col">
    <!-- Header -->
    <header class="bg-white border-b border-gray-200">
        <div class="flex items-center justify-between px-6 py-3">
            <div class="flex items-center space-x-4">
                <a href="{% url 'code_generator:generator' %}" class="flex items-center space-x-2 cursor-pointer">
                    <i class="fa-solid fa-arrow-left text-text-secondary hover:text-primary"></i>
                    <span class="text-text-secondary hover:text-primary">Back to Generator</span>
                </a>
                <div class="w-px h-6 bg-gray-300"></div>
                <div>
                    <h1 class="font-bold text-lg text-text-primary">{{ template_name|default:"PDF Template" }} - Code Preview</h1>
                    <p class="text-sm text-text-secondary">{{ language|title }} • {{ library|title }}</p>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                    <button id="copy-btn" class="px-4 py-2 bg-gray-100 text-text-primary rounded-md hover:bg-gray-200 transition duration-150">
                        <i class="fa-solid fa-copy mr-2"></i>Copy Code
                    </button>
                    <button id="download-btn" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-hover transition duration-150">
                        <i class="fa-solid fa-download mr-2"></i>Download
                    </button>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span class="text-xs text-text-secondary">Generated</span>
                </div>
            </div>
        </div>
    </header>

    <div class="flex h-full">
        <!-- File Explorer -->
        <div class="w-80 bg-gray-50 border-r border-gray-200 overflow-y-auto">
            <div class="p-4">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-sm font-semibold text-text-primary">Generated Files</h2>
                    <span class="text-xs text-text-secondary">{{ file_count|default:3 }} files</span>
                </div>
                
                <div class="space-y-1">
                    <!-- Main file -->
                    <div class="file-item p-3 rounded-lg cursor-pointer bg-primary/10 border border-primary/20" data-file="main">
                        <div class="flex items-center space-x-2">
                            <i class="fa-solid fa-file-code text-primary"></i>
                            <span class="text-sm font-medium text-text-primary">
                                {% if language == 'python' %}pdf_generator.py{% elif language == 'javascript' %}pdfGenerator.js{% else %}PDFGenerator.php{% endif %}
                            </span>
                        </div>
                        <p class="text-xs text-text-secondary mt-1">Main generator file</p>
                    </div>
                    
                    <!-- Config file -->
                    <div class="file-item p-3 rounded-lg cursor-pointer hover:bg-gray-100 transition duration-150" data-file="config">
                        <div class="flex items-center space-x-2">
                            <i class="fa-solid fa-cog text-gray-600"></i>
                            <span class="text-sm text-text-primary">
                                {% if language == 'python' %}config.py{% elif language == 'javascript' %}config.js{% else %}config.php{% endif %}
                            </span>
                        </div>
                        <p class="text-xs text-text-secondary mt-1">Configuration settings</p>
                    </div>
                    
                    <!-- Example file -->
                    <div class="file-item p-3 rounded-lg cursor-pointer hover:bg-gray-100 transition duration-150" data-file="example">
                        <div class="flex items-center space-x-2">
                            <i class="fa-solid fa-play text-green-600"></i>
                            <span class="text-sm text-text-primary">
                                {% if language == 'python' %}example.py{% elif language == 'javascript' %}example.js{% else %}example.php{% endif %}
                            </span>
                        </div>
                        <p class="text-xs text-text-secondary mt-1">Usage example</p>
                    </div>
                    
                    <!-- README -->
                    <div class="file-item p-3 rounded-lg cursor-pointer hover:bg-gray-100 transition duration-150" data-file="readme">
                        <div class="flex items-center space-x-2">
                            <i class="fa-solid fa-file-text text-blue-600"></i>
                            <span class="text-sm text-text-primary">README.md</span>
                        </div>
                        <p class="text-xs text-text-secondary mt-1">Documentation</p>
                    </div>
                    
                    <!-- Requirements -->
                    <div class="file-item p-3 rounded-lg cursor-pointer hover:bg-gray-100 transition duration-150" data-file="requirements">
                        <div class="flex items-center space-x-2">
                            <i class="fa-solid fa-list text-orange-600"></i>
                            <span class="text-sm text-text-primary">
                                {% if language == 'python' %}requirements.txt{% elif language == 'javascript' %}package.json{% else %}composer.json{% endif %}
                            </span>
                        </div>
                        <p class="text-xs text-text-secondary mt-1">Dependencies</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Code Editor -->
        <div class="flex-1 flex flex-col">
            <div class="bg-gray-100 border-b border-gray-200 px-4 py-2">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <span id="current-file-icon" class="text-primary">
                            <i class="fa-solid fa-file-code"></i>
                        </span>
                        <span id="current-file-name" class="text-sm font-medium text-text-primary">
                            {% if language == 'python' %}pdf_generator.py{% elif language == 'javascript' %}pdfGenerator.js{% else %}PDFGenerator.php{% endif %}
                        </span>
                    </div>
                    <div class="flex items-center space-x-2 text-xs text-text-secondary">
                        <span id="line-count">45 lines</span>
                        <span>•</span>
                        <span id="file-size">2.1 KB</span>
                    </div>
                </div>
            </div>
            
            <div class="flex-1 overflow-hidden">
                <pre id="code-content" class="h-full overflow-auto bg-gray-900 text-green-400 p-6 text-sm font-mono leading-relaxed"><code id="code-display"># Generated PDF Generator for {{ template_name|default:"Contact Form" }}
# Language: {{ language|title }}
# Library: {{ library|title }}
# Generated on: {% now "Y-m-d H:i:s" %}

{% if language == 'python' %}import {{ library }}
from datetime import datetime
import os

class PDFGenerator:
    """
    PDF Generator for {{ template_name|default:"Contact Form" }}
    Generated automatically by PDFlex
    """
    
    def __init__(self, template_config=None):
        self.template_config = template_config or {}
        self.output_dir = "generated_pdfs"
        
    def generate_pdf(self, data):
        """
        Generate PDF from form data
        
        Args:
            data (dict): Form submission data
            
        Returns:
            str: Path to generated PDF file
        """
        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Generate filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{{ template_name|default:'contact_form'|slugify }}_{timestamp}.pdf"
        filepath = os.path.join(self.output_dir, filename)
        
        # Initialize PDF canvas
        {% if library == 'reportlab' %}from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        
        c = canvas.Canvas(filepath, pagesize=letter)
        width, height = letter
        
        # Add content to PDF
        c.drawString(100, height - 100, f"Name: {data.get('name', '')}")
        c.drawString(100, height - 130, f"Email: {data.get('email', '')}")
        c.drawString(100, height - 160, f"Message: {data.get('message', '')}")
        
        # Add footer
        c.drawString(100, 50, f"Generated by PDFlex on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        c.save(){% else %}# FPDF2 implementation
        from fpdf import FPDF
        
        pdf = FPDF()
        pdf.add_page()
        pdf.set_font('Arial', 'B', 16)
        
        # Add content
        pdf.cell(40, 10, f"Name: {data.get('name', '')}")
        pdf.ln(10)
        pdf.cell(40, 10, f"Email: {data.get('email', '')}")
        pdf.ln(10)
        pdf.cell(40, 10, f"Message: {data.get('message', '')}")
        
        pdf.output(filepath){% endif %}
        
        return filepath
    
    def validate_data(self, data):
        """Validate form data before PDF generation"""
        required_fields = ['name', 'email']
        
        for field in required_fields:
            if not data.get(field):
                raise ValueError(f"Missing required field: {field}")
        
        return True

# Example usage
if __name__ == "__main__":
    generator = PDFGenerator()
    
    sample_data = {
        "name": "John Doe",
        "email": "<EMAIL>",
        "message": "Hello from PDFlex!"
    }
    
    try:
        pdf_path = generator.generate_pdf(sample_data)
        print(f"PDF generated successfully: {pdf_path}")
    except Exception as e:
        print(f"Error generating PDF: {e}"){% elif language == 'javascript' %}const {{ library }} = require('{{ library }}');
const fs = require('fs');
const path = require('path');

class PDFGenerator {
    /**
     * PDF Generator for {{ template_name|default:"Contact Form" }}
     * Generated automatically by PDFlex
     */
    
    constructor(templateConfig = {}) {
        this.templateConfig = templateConfig;
        this.outputDir = 'generated_pdfs';
    }
    
    async generatePDF(data) {
        /**
         * Generate PDF from form data
         * @param {Object} data - Form submission data
         * @returns {string} Path to generated PDF file
         */
        
        // Create output directory
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
        
        // Generate filename
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `{{ template_name|default:'contact_form'|slugify }}_${timestamp}.pdf`;
        const filepath = path.join(this.outputDir, filename);
        
        {% if library == 'jspdf' %}// jsPDF implementation
        const { jsPDF } = require('jspdf');
        const doc = new jsPDF();
        
        // Add content
        doc.text(`Name: ${data.name || ''}`, 20, 20);
        doc.text(`Email: ${data.email || ''}`, 20, 30);
        doc.text(`Message: ${data.message || ''}`, 20, 40);
        
        // Add footer
        doc.text(`Generated by PDFlex on ${new Date().toLocaleString()}`, 20, 280);
        
        // Save PDF
        doc.save(filepath);{% else %}// PDF-lib implementation
        const { PDFDocument, rgb } = require('pdf-lib');
        
        const pdfDoc = await PDFDocument.create();
        const page = pdfDoc.addPage([600, 400]);
        
        // Add content
        page.drawText(`Name: ${data.name || ''}`, {
            x: 50,
            y: 350,
            size: 12,
            color: rgb(0, 0, 0),
        });
        
        page.drawText(`Email: ${data.email || ''}`, {
            x: 50,
            y: 330,
            size: 12,
            color: rgb(0, 0, 0),
        });
        
        page.drawText(`Message: ${data.message || ''}`, {
            x: 50,
            y: 310,
            size: 12,
            color: rgb(0, 0, 0),
        });
        
        const pdfBytes = await pdfDoc.save();
        fs.writeFileSync(filepath, pdfBytes);{% endif %}
        
        return filepath;
    }
    
    validateData(data) {
        const requiredFields = ['name', 'email'];
        
        for (const field of requiredFields) {
            if (!data[field]) {
                throw new Error(`Missing required field: ${field}`);
            }
        }
        
        return true;
    }
}

// Example usage
if (require.main === module) {
    const generator = new PDFGenerator();
    
    const sampleData = {
        name: 'John Doe',
        email: '<EMAIL>',
        message: 'Hello from PDFlex!'
    };
    
    generator.generatePDF(sampleData)
        .then(pdfPath => {
            console.log(`PDF generated successfully: ${pdfPath}`);
        })
        .catch(error => {
            console.error(`Error generating PDF: ${error.message}`);
        });
}

module.exports = PDFGenerator;{% else %}<?php
/**
 * PDF Generator for {{ template_name|default:"Contact Form" }}
 * Generated automatically by PDFlex
 * Language: PHP
 * Library: {{ library|title }}
 */

{% if library == 'tcpdf' %}require_once('tcpdf/tcpdf.php');{% else %}require_once('fpdf/fpdf.php');{% endif %}

class PDFGenerator {
    private $templateConfig;
    private $outputDir;
    
    public function __construct($templateConfig = []) {
        $this->templateConfig = $templateConfig;
        $this->outputDir = 'generated_pdfs';
    }
    
    public function generatePDF($data) {
        // Create output directory
        if (!is_dir($this->outputDir)) {
            mkdir($this->outputDir, 0755, true);
        }
        
        // Generate filename
        $timestamp = date('Y-m-d_H-i-s');
        $filename = "{{ template_name|default:'contact_form'|slugify }}_{$timestamp}.pdf";
        $filepath = $this->outputDir . '/' . $filename;
        
        {% if library == 'tcpdf' %}// TCPDF implementation
        $pdf = new TCPDF();
        $pdf->AddPage();
        $pdf->SetFont('helvetica', '', 12);
        
        // Add content
        $pdf->Cell(0, 10, 'Name: ' . ($data['name'] ?? ''), 0, 1);
        $pdf->Cell(0, 10, 'Email: ' . ($data['email'] ?? ''), 0, 1);
        $pdf->Cell(0, 10, 'Message: ' . ($data['message'] ?? ''), 0, 1);
        
        // Add footer
        $pdf->SetY(-30);
        $pdf->Cell(0, 10, 'Generated by PDFlex on ' . date('Y-m-d H:i:s'), 0, 1);
        
        $pdf->Output($filepath, 'F');{% else %}// FPDF implementation
        $pdf = new FPDF();
        $pdf->AddPage();
        $pdf->SetFont('Arial', 'B', 16);
        
        // Add content
        $pdf->Cell(40, 10, 'Name: ' . ($data['name'] ?? ''));
        $pdf->Ln();
        $pdf->Cell(40, 10, 'Email: ' . ($data['email'] ?? ''));
        $pdf->Ln();
        $pdf->Cell(40, 10, 'Message: ' . ($data['message'] ?? ''));
        
        $pdf->Output('F', $filepath);{% endif %}
        
        return $filepath;
    }
    
    public function validateData($data) {
        $requiredFields = ['name', 'email'];
        
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                throw new Exception("Missing required field: {$field}");
            }
        }
        
        return true;
    }
}

// Example usage
if (basename(__FILE__) == basename($_SERVER['PHP_SELF'])) {
    $generator = new PDFGenerator();
    
    $sampleData = [
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'message' => 'Hello from PDFlex!'
    ];
    
    try {
        $pdfPath = $generator->generatePDF($sampleData);
        echo "PDF generated successfully: {$pdfPath}\n";
    } catch (Exception $e) {
        echo "Error generating PDF: " . $e->getMessage() . "\n";
    }
}
?>{% endif %}</code></pre>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // File switching functionality
    document.addEventListener('DOMContentLoaded', function() {
        const fileItems = document.querySelectorAll('.file-item');
        const codeDisplay = document.getElementById('code-display');
        const currentFileName = document.getElementById('current-file-name');
        const currentFileIcon = document.getElementById('current-file-icon');
        
        // File content templates
        const fileContents = {
            main: document.getElementById('code-display').textContent,
            config: `# Configuration file for PDF Generator
# Modify these settings as needed

PDF_CONFIG = {
    'page_size': 'letter',
    'orientation': 'portrait',
    'margins': {
        'top': 50,
        'bottom': 50,
        'left': 50,
        'right': 50
    },
    'font': {
        'family': 'Arial',
        'size': 12
    }
}`,
            example: `# Example usage of the PDF Generator

from pdf_generator import PDFGenerator

# Initialize generator
generator = PDFGenerator()

# Sample data
data = {
    'name': 'Jane Smith',
    'email': '<EMAIL>',
    'message': 'This is a test message'
}

# Generate PDF
pdf_path = generator.generate_pdf(data)
print(f"Generated: {pdf_path}")`,
            readme: `# PDF Generator

This package was automatically generated by PDFlex.

## Installation

\`\`\`bash
pip install -r requirements.txt
\`\`\`

## Usage

\`\`\`python
from pdf_generator import PDFGenerator

generator = PDFGenerator()
pdf_path = generator.generate_pdf(your_data)
\`\`\`

## Configuration

Edit \`config.py\` to customize PDF settings.`,
            requirements: `reportlab==3.6.0
fpdf2==2.7.4
python-dateutil==2.8.2`
        };
        
        fileItems.forEach(item => {
            item.addEventListener('click', function() {
                // Remove active state from all items
                fileItems.forEach(i => {
                    i.classList.remove('bg-primary/10', 'border-primary/20');
                    i.classList.add('hover:bg-gray-100');
                });
                
                // Add active state to clicked item
                this.classList.add('bg-primary/10', 'border-primary/20');
                this.classList.remove('hover:bg-gray-100');
                
                // Update content
                const fileType = this.dataset.file;
                const fileName = this.querySelector('span:nth-child(2)').textContent;
                const icon = this.querySelector('i').className;
                
                currentFileName.textContent = fileName;
                currentFileIcon.innerHTML = `<i class="${icon}"></i>`;
                codeDisplay.textContent = fileContents[fileType] || fileContents.main;
            });
        });
        
        // Copy functionality
        document.getElementById('copy-btn').addEventListener('click', function() {
            const code = codeDisplay.textContent;
            navigator.clipboard.writeText(code).then(() => {
                this.innerHTML = '<i class="fa-solid fa-check mr-2"></i>Copied!';
                setTimeout(() => {
                    this.innerHTML = '<i class="fa-solid fa-copy mr-2"></i>Copy Code';
                }, 2000);
            });
        });
        
        // Download functionality
        document.getElementById('download-btn').addEventListener('click', function() {
            // In a real implementation, this would trigger a download
            alert('Download functionality would be implemented here');
        });
    });
</script>
{% endblock %}
