{% extends 'base.html' %}

{% block title %}Code Generator - PDFlex{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-text-primary">Code Generator</h1>
                <p class="mt-2 text-text-secondary">Generate code for your PDF templates in multiple languages</p>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Template Selection -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-text-primary">Select Template</h2>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    {% for template in templates %}
                    <div class="template-item p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition duration-150" 
                         data-template-id="{{ template.id }}" onclick="selectTemplate(this)">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                                <i class="fa-solid fa-file-pdf text-primary"></i>
                            </div>
                            <div>
                                <h3 class="font-medium text-text-primary">{{ template.name }}</h3>
                                <p class="text-sm text-text-secondary">{{ template.elements.count }} elements</p>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-8">
                        <i class="fa-solid fa-file-pdf text-gray-400 text-3xl mb-3"></i>
                        <p class="text-text-secondary">No templates available</p>
                        <a href="{% url 'pdf_generation:template_list' %}" class="text-primary hover:text-primary-hover text-sm">Create a template first</a>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Language & Options -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-text-primary">Generation Options</h2>
            </div>
            <div class="p-6 space-y-6">
                <!-- Language Selection -->
                <div>
                    <label class="block text-sm font-medium text-text-primary mb-3">Programming Language</label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="radio" name="language" value="python" class="text-primary focus:ring-primary" checked>
                            <span class="ml-2 flex items-center">
                                <i class="fa-brands fa-python text-blue-500 mr-2"></i>
                                Python (ReportLab/FPDF2)
                            </span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="language" value="javascript" class="text-primary focus:ring-primary">
                            <span class="ml-2 flex items-center">
                                <i class="fa-brands fa-js text-yellow-500 mr-2"></i>
                                JavaScript (jsPDF/PDFLib)
                            </span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="language" value="php" class="text-primary focus:ring-primary">
                            <span class="ml-2 flex items-center">
                                <i class="fa-brands fa-php text-purple-500 mr-2"></i>
                                PHP (TCPDF/FPDF)
                            </span>
                        </label>
                    </div>
                </div>

                <!-- Library Selection -->
                <div id="library-selection">
                    <label class="block text-sm font-medium text-text-primary mb-3">PDF Library</label>
                    <select id="library-select" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary">
                        <option value="reportlab">ReportLab</option>
                        <option value="fpdf2">FPDF2</option>
                        <option value="weasyprint">WeasyPrint</option>
                    </select>
                </div>

                <!-- Generation Options -->
                <div>
                    <label class="block text-sm font-medium text-text-primary mb-3">Options</label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" id="include-comments" class="text-primary focus:ring-primary" checked>
                            <span class="ml-2 text-sm">Include comments</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" id="include-validation" class="text-primary focus:ring-primary">
                            <span class="ml-2 text-sm">Include data validation</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" id="include-examples" class="text-primary focus:ring-primary" checked>
                            <span class="ml-2 text-sm">Include usage examples</span>
                        </label>
                    </div>
                </div>

                <!-- Generate Button -->
                <button id="generate-btn" class="w-full bg-primary hover:bg-primary-hover text-white px-4 py-3 rounded-lg transition duration-150 disabled:opacity-50" disabled>
                    <i class="fa-solid fa-code mr-2"></i>
                    Generate Code
                </button>
            </div>
        </div>

        <!-- Generation Status & Preview -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-text-primary">Generation Status</h2>
            </div>
            <div class="p-6">
                <div id="generation-idle" class="text-center py-8">
                    <i class="fa-solid fa-code text-gray-400 text-3xl mb-3"></i>
                    <p class="text-text-secondary">Select a template and click generate to start</p>
                </div>

                <div id="generation-progress" class="hidden">
                    <div class="flex items-center mb-4">
                        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mr-3"></div>
                        <span class="text-text-primary">Generating code...</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="progress-bar" class="bg-primary h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <div class="mt-2 text-sm text-text-secondary">
                        <span id="progress-text">Initializing...</span>
                    </div>
                </div>

                <div id="generation-complete" class="hidden">
                    <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg mb-4">
                        <i class="fa-solid fa-check-circle mr-2"></i>
                        Code generated successfully!
                    </div>
                    
                    <div class="space-y-3">
                        <button id="download-btn" class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition duration-150">
                            <i class="fa-solid fa-download mr-2"></i>
                            Download Code Package
                        </button>
                        
                        <button id="preview-btn" class="w-full border border-gray-300 text-text-primary px-4 py-2 rounded-lg hover:bg-gray-50 transition duration-150">
                            <i class="fa-solid fa-eye mr-2"></i>
                            Preview Code
                        </button>
                    </div>
                </div>

                <div id="generation-error" class="hidden">
                    <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
                        <i class="fa-solid fa-exclamation-circle mr-2"></i>
                        <span id="error-message">Generation failed</span>
                    </div>
                    <button onclick="resetGeneration()" class="w-full border border-gray-300 text-text-primary px-4 py-2 rounded-lg hover:bg-gray-50 transition duration-150">
                        <i class="fa-solid fa-redo mr-2"></i>
                        Try Again
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Code Preview Modal -->
<div id="codePreviewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-text-primary">Code Preview</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closePreviewModal()">
                <i class="fa-solid fa-times"></i>
            </button>
        </div>
        
        <div class="mb-4">
            <pre id="code-preview" class="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto text-sm"><code>// Generated code will appear here...</code></pre>
        </div>
        
        <div class="flex justify-end space-x-3">
            <button onclick="copyCode()" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition duration-150">
                <i class="fa-solid fa-copy mr-2"></i>
                Copy Code
            </button>
            <button onclick="closePreviewModal()" class="px-4 py-2 border border-gray-300 text-text-primary rounded-lg hover:bg-gray-50 transition duration-150">
                Close
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedTemplateId = null;
let generatedCodeId = null;

function selectTemplate(element) {
    // Remove previous selection
    document.querySelectorAll('.template-item').forEach(item => {
        item.classList.remove('border-primary', 'bg-blue-50');
    });
    
    // Add selection to current item
    element.classList.add('border-primary', 'bg-blue-50');
    selectedTemplateId = element.dataset.templateId;
    
    // Enable generate button
    document.getElementById('generate-btn').disabled = false;
}

function generateCode() {
    if (!selectedTemplateId) return;
    
    const language = document.querySelector('input[name="language"]:checked').value;
    const library = document.getElementById('library-select').value;
    const options = {
        includeComments: document.getElementById('include-comments').checked,
        includeValidation: document.getElementById('include-validation').checked,
        includeExamples: document.getElementById('include-examples').checked
    };
    
    // Show progress
    document.getElementById('generation-idle').classList.add('hidden');
    document.getElementById('generation-progress').classList.remove('hidden');
    
    // Simulate progress
    let progress = 0;
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');
    
    const progressSteps = [
        'Analyzing template structure...',
        'Generating code framework...',
        'Adding PDF elements...',
        'Optimizing output...',
        'Finalizing package...'
    ];
    
    const progressInterval = setInterval(() => {
        progress += 20;
        progressBar.style.width = progress + '%';
        
        if (progress <= 100) {
            progressText.textContent = progressSteps[Math.floor(progress / 20) - 1] || 'Completing...';
        }
        
        if (progress >= 100) {
            clearInterval(progressInterval);
            setTimeout(() => {
                showGenerationComplete();
            }, 500);
        }
    }, 800);
}

function showGenerationComplete() {
    document.getElementById('generation-progress').classList.add('hidden');
    document.getElementById('generation-complete').classList.remove('hidden');
    generatedCodeId = 'sample-code-id-' + Date.now();
}

function resetGeneration() {
    document.getElementById('generation-error').classList.add('hidden');
    document.getElementById('generation-complete').classList.add('hidden');
    document.getElementById('generation-progress').classList.add('hidden');
    document.getElementById('generation-idle').classList.remove('hidden');
}

function closePreviewModal() {
    document.getElementById('codePreviewModal').classList.add('hidden');
}

function copyCode() {
    const codeElement = document.getElementById('code-preview');
    navigator.clipboard.writeText(codeElement.textContent);
    // Show success message
    alert('Code copied to clipboard!');
}

// Event listeners
document.getElementById('generate-btn').addEventListener('click', generateCode);

document.getElementById('download-btn').addEventListener('click', function() {
    if (generatedCodeId) {
        window.location.href = `/code-generator/download/${generatedCodeId}/`;
    }
});

document.getElementById('preview-btn').addEventListener('click', function() {
    document.getElementById('codePreviewModal').classList.remove('hidden');
    // In a real implementation, you'd fetch the actual generated code
    document.getElementById('code-preview').innerHTML = `<code># Generated Python code for PDF template
import reportlab
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter

def generate_pdf(data):
    """Generate PDF from template data"""
    c = canvas.Canvas("output.pdf", pagesize=letter)
    
    # Add your PDF generation logic here
    c.drawString(100, 750, f"Name: {data.get('name', '')}")
    c.drawString(100, 730, f"Email: {data.get('email', '')}")
    
    c.save()
    return "output.pdf"

# Example usage
if __name__ == "__main__":
    sample_data = {
        "name": "John Doe",
        "email": "<EMAIL>"
    }
    generate_pdf(sample_data)</code>`;
});

// Language change handler
document.querySelectorAll('input[name="language"]').forEach(radio => {
    radio.addEventListener('change', function() {
        const librarySelect = document.getElementById('library-select');
        librarySelect.innerHTML = '';
        
        if (this.value === 'python') {
            librarySelect.innerHTML = `
                <option value="reportlab">ReportLab</option>
                <option value="fpdf2">FPDF2</option>
                <option value="weasyprint">WeasyPrint</option>
            `;
        } else if (this.value === 'javascript') {
            librarySelect.innerHTML = `
                <option value="jspdf">jsPDF</option>
                <option value="pdflib">PDF-lib</option>
                <option value="puppeteer">Puppeteer</option>
            `;
        } else if (this.value === 'php') {
            librarySelect.innerHTML = `
                <option value="tcpdf">TCPDF</option>
                <option value="fpdf">FPDF</option>
                <option value="dompdf">DomPDF</option>
            `;
        }
    });
});
</script>
{% endblock %}
