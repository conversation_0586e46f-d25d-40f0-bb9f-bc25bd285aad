<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      {% block title %}
        PDFlex - Universal Form-to-PDF Generator
      {% endblock %}
    </title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- FontAwesome -->
    <script>
      window.FontAwesomeConfig = { autoReplaceSvg: 'nest' }
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <!-- Inter Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" />

    <!-- Custom CSS -->
    <style>
      ::-webkit-scrollbar {
        display: none;
      }
      
      :root {
        --primary: #4f46e5;
        --primary-hover: #4338ca;
        --secondary: #f3f4f6;
        --text-primary: #111827;
        --text-secondary: #6b7280;
        --border: #e5e7eb;
        --success: #10b981;
        --warning: #f59e0b;
        --error: #ef4444;
      }
      
      body {
        font-family: 'Inter', sans-serif;
        -ms-overflow-style: none;
        scrollbar-width: none;
      }
      
      .btn-primary {
        background-color: var(--primary);
        border-color: var(--primary);
        color: white;
        transition: all 0.3s ease;
      }
      
      .btn-primary:hover {
        background-color: var(--primary-hover);
        border-color: var(--primary-hover);
        color: white;
        transform: translateY(-1px);
      }
      
      .card {
        transition: all 0.3s ease;
        border: 1px solid var(--border);
      }
      
      .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      }
    </style>

    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: '#4f46e5',
              'primary-hover': '#4338ca',
              secondary: '#f3f4f6',
              'text-primary': '#111827',
              'text-secondary': '#6b7280',
              border: '#e5e7eb'
            },
            fontFamily: {
              sans: ['Inter', 'sans-serif']
            }
          }
        }
      }
    </script>

    {% block extra_css %}

    {% endblock %}
  </head>
  <body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center space-x-4">
            <a href="{% url 'core:home' %}" class="flex items-center space-x-2">
              <i class="fa-solid fa-file-pdf text-primary text-2xl"></i>
              <span class="font-bold text-xl text-text-primary">PDFlex</span>
            </a>
            {% if user.is_authenticated %}
              <nav class="hidden md:flex items-center space-x-6 ml-8">
                <a href="{% url 'users:dashboard' %}" class="text-text-secondary hover:text-primary transition duration-150 cursor-pointer"><i class="fa-solid fa-tachometer-alt mr-1"></i> Dashboard</a>
                <a href="{% url 'forms:form_list' %}" class="text-text-secondary hover:text-primary transition duration-150 cursor-pointer"><i class="fa-solid fa-file-text mr-1"></i> Forms</a>
                <a href="{% url 'pdf_generation:template_list' %}" class="text-text-secondary hover:text-primary transition duration-150 cursor-pointer"><i class="fa-solid fa-file-pdf mr-1"></i> Templates</a>
                <a href="{% url 'code_generator:generator' %}" class="text-text-secondary hover:text-primary transition duration-150 cursor-pointer"><i class="fa-solid fa-code mr-1"></i> Code Generator</a>
                <a href="{% url 'webhooks:configuration' %}" class="text-text-secondary hover:text-primary transition duration-150 cursor-pointer"><i class="fa-solid fa-webhook mr-1"></i> Webhooks</a>
              </nav>

              <!-- Mobile menu button -->
              <button id="mobile-menu-button" class="md:hidden text-text-secondary hover:text-primary"><i class="fa-solid fa-bars text-xl"></i></button>
            {% endif %}
          </div>
          <div class="flex items-center space-x-4">
            {% if user.is_authenticated %}
              <div class="relative">
                <button id="user-menu-button" class="flex items-center space-x-2 text-text-secondary hover:text-primary transition duration-150 cursor-pointer">
                  <i class="fa-solid fa-user-circle text-xl"></i>
                  <span class="hidden md:block">{{ user.get_display_name }}</span>
                  <i class="fa-solid fa-chevron-down text-xs"></i>
                </button>
                <div id="user-menu" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden">
                  <a href="{% url 'users:profile' %}" class="block px-4 py-2 text-sm text-text-primary hover:bg-gray-100"><i class="fa-solid fa-user mr-2"></i> Profile</a>
                  <a href="{% url 'users:account_edit' %}" class="block px-4 py-2 text-sm text-text-primary hover:bg-gray-100"><i class="fa-solid fa-cog mr-2"></i> Settings</a>
                  <hr class="my-1" />
                  <a href="{% url 'users:logout' %}" class="block px-4 py-2 text-sm text-text-primary hover:bg-gray-100"><i class="fa-solid fa-sign-out-alt mr-2"></i> Logout</a>
                </div>
              </div>
            {% else %}
              <a href="{% url 'users:login' %}" class="text-text-secondary hover:text-primary transition duration-150">Sign In</a>
              <a href="{% url 'users:register' %}" class="bg-primary hover:bg-primary-hover text-white px-4 py-2 rounded-lg transition duration-150">Get Started</a>
            {% endif %}
          </div>
        </div>
      </div>
    </header>

    <!-- Mobile Navigation Menu -->
    {% if user.is_authenticated %}
      <div id="mobile-menu" class="md:hidden bg-white border-b border-gray-200 hidden">
        <div class="px-4 py-2 space-y-1">
          <a href="{% url 'users:dashboard' %}" class="block px-3 py-2 text-text-secondary hover:text-primary hover:bg-gray-50 rounded-md transition duration-150"><i class="fa-solid fa-tachometer-alt mr-2"></i> Dashboard</a>
          <a href="{% url 'forms:form_list' %}" class="block px-3 py-2 text-text-secondary hover:text-primary hover:bg-gray-50 rounded-md transition duration-150"><i class="fa-solid fa-file-text mr-2"></i> Forms</a>
          <a href="{% url 'pdf_generation:template_list' %}" class="block px-3 py-2 text-text-secondary hover:text-primary hover:bg-gray-50 rounded-md transition duration-150"><i class="fa-solid fa-file-pdf mr-2"></i> Templates</a>
          <a href="{% url 'code_generator:generator' %}" class="block px-3 py-2 text-text-secondary hover:text-primary hover:bg-gray-50 rounded-md transition duration-150"><i class="fa-solid fa-code mr-2"></i> Code Generator</a>
          <a href="{% url 'webhooks:configuration' %}" class="block px-3 py-2 text-text-secondary hover:text-primary hover:bg-gray-50 rounded-md transition duration-150"><i class="fa-solid fa-webhook mr-2"></i> Webhooks</a>
        </div>
      </div>
    {% endif %}

    <!-- Messages -->
    {% if messages %}
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-4">
        {% for message in messages %}
          <div class="mb-4 p-4 rounded-lg border-l-4 {% if message.tags == 'error' %}
              
              
              
              
              
              
              
              bg-red-50 border-red-400 text-red-700







            {% elif message.tags == 'warning' %}
              
              
              
              
              
              
              
              bg-yellow-50 border-yellow-400 text-yellow-700







            {% elif message.tags == 'success' %}
              
              
              
              
              
              
              
              bg-green-50 border-green-400 text-green-700







            {% else %}
              
              
              
              
              
              
              
              bg-blue-50 border-blue-400 text-blue-700







            {% endif %}"
            role="alert">
            <div class="flex items-center">
              <i class="fa-solid fa-{% if message.tags == 'error' %}
                  
                  
                  
                  
                  
                  
                  
                  exclamation-triangle







                {% elif message.tags == 'warning' %}
                  
                  
                  
                  
                  
                  
                  
                  exclamation-circle







                {% elif message.tags == 'success' %}
                  
                  
                  
                  
                  
                  
                  
                  check-circle







                {% else %}
                  
                  
                  
                  
                  
                  
                  
                  info-circle







                {% endif %} mr-2">

              </i>
              <span>{{ message }}</span>
              <button type="button" class="ml-auto text-gray-400 hover:text-gray-600" onclick="this.parentElement.parentElement.remove()"><i class="fa-solid fa-times"></i></button>
            </div>
          </div>
        {% endfor %}
      </div>
    {% endif %}

    <main class="flex-grow">
      {% block content %}

      {% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12 mt-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <div class="flex items-center space-x-2 mb-4">
              <i class="fa-solid fa-file-pdf text-primary text-2xl"></i>
              <span class="font-bold text-xl">PDFlex</span>
            </div>
            <p class="text-gray-400">Transform forms into professional PDFs with our visual builder and multi-language code export.</p>
          </div>

          <div>
            <h4 class="font-semibold mb-4">Product</h4>
            <ul class="space-y-2 text-gray-400">
              <li>
                <a href="{% url 'core:landing' %}" class="hover:text-white transition duration-150 cursor-pointer">Features</a>
              </li>
              <li>
                <a href="{% url 'core:landing' %}#pricing" class="hover:text-white transition duration-150 cursor-pointer">Pricing</a>
              </li>
              <li>
                <a href="{% url 'api:api_root' %}" class="hover:text-white transition duration-150 cursor-pointer">API</a>
              </li>
              <li>
                <a href="{% url 'api:documentation' %}" class="hover:text-white transition duration-150 cursor-pointer">Documentation</a>
              </li>
            </ul>
          </div>

          <div>
            <h4 class="font-semibold mb-4">Company</h4>
            <ul class="space-y-2 text-gray-400">
              <li>
                <a href="{% url 'core:landing' %}#about" class="hover:text-white transition duration-150 cursor-pointer">About</a>
              </li>
              <li>
                <a href="{% url 'core:home' %}" class="hover:text-white transition duration-150 cursor-pointer">Blog</a>
              </li>
              <li>
                <a href="{% url 'core:home' %}" class="hover:text-white transition duration-150 cursor-pointer">Careers</a>
              </li>
              <li>
                <a href="{% url 'core:home' %}" class="hover:text-white transition duration-150 cursor-pointer">Contact</a>
              </li>
            </ul>
          </div>

          <div>
            <h4 class="font-semibold mb-4">Support</h4>
            <ul class="space-y-2 text-gray-400">
              <li>
                <a href="{% url 'api:documentation' %}" class="hover:text-white transition duration-150 cursor-pointer">Help Center</a>
              </li>
              <li>
                <a href="{% url 'core:home' %}" class="hover:text-white transition duration-150 cursor-pointer">Community</a>
              </li>
              <li>
                <a href="{% url 'core:home' %}" class="hover:text-white transition duration-150 cursor-pointer">Status</a>
              </li>
              <li>
                <a href="{% url 'core:home' %}" class="hover:text-white transition duration-150 cursor-pointer">Privacy</a>
              </li>
            </ul>
          </div>
        </div>

        <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; 2024 PDFlex - Universal Form-to-PDF Generator. All rights reserved.</p>
        </div>
      </div>
    </footer>

    <!-- JavaScript -->
    <script>
      // User menu toggle
      document.addEventListener('DOMContentLoaded', function () {
        const userMenuButton = document.getElementById('user-menu-button')
        const userMenu = document.getElementById('user-menu')
      
        if (userMenuButton && userMenu) {
          userMenuButton.addEventListener('click', function () {
            userMenu.classList.toggle('hidden')
          })
      
          // Close menu when clicking outside
          document.addEventListener('click', function (event) {
            if (!userMenuButton.contains(event.target) && !userMenu.contains(event.target)) {
              userMenu.classList.add('hidden')
            }
          })
        }
      
        // Mobile menu toggle
        const mobileMenuButton = document.getElementById('mobile-menu-button')
        const mobileMenu = document.getElementById('mobile-menu')
      
        if (mobileMenuButton && mobileMenu) {
          mobileMenuButton.addEventListener('click', function () {
            mobileMenu.classList.toggle('hidden')
          })
        }
      
        // Auto-hide messages after 5 seconds
        setTimeout(function () {
          const messages = document.querySelectorAll('[role="alert"]')
          messages.forEach(function (message) {
            message.remove()
          })
        }, 5000)
      })
    </script>

    {% block extra_js %}

    {% endblock %}
  </body>
</html>
