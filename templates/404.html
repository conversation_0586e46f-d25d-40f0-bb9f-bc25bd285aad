{% extends 'base.html' %}

{% block title %}Page Not Found - PDFlex{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full text-center">
        <div class="mb-8">
            <div class="mx-auto h-32 w-32 flex items-center justify-center rounded-full bg-gray-100 mb-6">
                <i class="fa-solid fa-search text-gray-400 text-5xl"></i>
            </div>
            <h1 class="text-6xl font-bold text-text-primary mb-4">404</h1>
            <h2 class="text-2xl font-semibold text-text-primary mb-4">Page Not Found</h2>
            <p class="text-text-secondary mb-8">
                Sorry, we couldn't find the page you're looking for. The page might have been moved, deleted, or you entered the wrong URL.
            </p>
        </div>
        
        <div class="space-y-4">
            <a href="{% url 'core:home' %}" class="w-full bg-primary hover:bg-primary-hover text-white px-6 py-3 rounded-lg transition duration-150 inline-flex items-center justify-center">
                <i class="fa-solid fa-home mr-2"></i>
                Go Home
            </a>
            
            <button onclick="history.back()" class="w-full border border-gray-300 text-text-primary px-6 py-3 rounded-lg hover:bg-gray-50 transition duration-150 inline-flex items-center justify-center">
                <i class="fa-solid fa-arrow-left mr-2"></i>
                Go Back
            </button>
        </div>
        
        <div class="mt-8 pt-8 border-t border-gray-200">
            <p class="text-sm text-text-secondary mb-4">Need help? Try these popular pages:</p>
            <div class="space-y-2">
                <a href="{% url 'users:dashboard' %}" class="block text-primary hover:text-primary-hover text-sm">Dashboard</a>
                <a href="{% url 'forms:form_list' %}" class="block text-primary hover:text-primary-hover text-sm">Forms</a>
                <a href="{% url 'pdf_generation:template_list' %}" class="block text-primary hover:text-primary-hover text-sm">PDF Templates</a>
                <a href="#" class="block text-primary hover:text-primary-hover text-sm">Help Center</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Track 404 errors for analytics
    document.addEventListener('DOMContentLoaded', function() {
        console.log('404 Error - Page not found:', window.location.pathname);
        
        // You could send this to your analytics service
        // analytics.track('404_error', { path: window.location.pathname });
    });
</script>
{% endblock %}
