{% extends 'base.html' %}

{% block title %}Edit Webhook - PDFlex{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center space-x-4">
            <a href="{% url 'webhooks:configuration' %}" class="flex items-center space-x-2 text-text-secondary hover:text-primary transition duration-150">
                <i class="fa-solid fa-arrow-left"></i>
                <span>Back to Configuration</span>
            </a>
        </div>
        <h1 class="text-3xl font-bold text-text-primary mt-4">Edit Webhook</h1>
        <p class="mt-2 text-text-secondary">Update webhook configuration and settings</p>
    </div>

    <!-- Edit Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <form method="post" class="p-6 space-y-6">
            {% csrf_token %}
            
            <!-- Webhook URL -->
            <div>
                <label for="webhook_url" class="block text-sm font-medium text-text-primary mb-2">
                    <i class="fa-solid fa-link mr-1"></i>
                    Webhook URL
                </label>
                <input type="url" id="webhook_url" name="webhook_url" required
                       value="{{ webhook.url }}"
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary">
                <p class="mt-1 text-sm text-text-secondary">The URL where webhook notifications will be sent</p>
            </div>

            <!-- Webhook Name -->
            <div>
                <label for="webhook_name" class="block text-sm font-medium text-text-primary mb-2">
                    <i class="fa-solid fa-tag mr-1"></i>
                    Webhook Name
                </label>
                <input type="text" id="webhook_name" name="webhook_name" required
                       value="{{ webhook.name|default:'My Webhook' }}"
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary">
                <p class="mt-1 text-sm text-text-secondary">A friendly name to identify this webhook</p>
            </div>

            <!-- Events -->
            <div>
                <label class="block text-sm font-medium text-text-primary mb-3">
                    <i class="fa-solid fa-bell mr-1"></i>
                    Events to Subscribe
                </label>
                <div class="space-y-3">
                    {% for event in webhook_events %}
                    <label class="flex items-center">
                        <input type="checkbox" name="events" value="{{ event }}" 
                               {% if event in webhook.events %}checked{% endif %}
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <span class="ml-3 text-sm text-text-primary">{{ event }}</span>
                    </label>
                    {% endfor %}
                </div>
                <p class="mt-2 text-sm text-text-secondary">Select which events should trigger this webhook</p>
            </div>

            <!-- Secret -->
            <div>
                <label for="webhook_secret" class="block text-sm font-medium text-text-primary mb-2">
                    <i class="fa-solid fa-key mr-1"></i>
                    Secret (Optional)
                </label>
                <input type="text" id="webhook_secret" name="webhook_secret"
                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                       placeholder="Enter a secret for webhook verification">
                <p class="mt-1 text-sm text-text-secondary">Used to verify webhook authenticity (leave blank to keep current)</p>
            </div>

            <!-- Active Status -->
            <div class="flex items-center">
                <input type="checkbox" id="is_active" name="is_active" 
                       {% if webhook.is_active %}checked{% endif %}
                       class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                <label for="is_active" class="ml-3 text-sm text-text-primary">
                    <i class="fa-solid fa-toggle-on mr-1"></i>
                    Active
                </label>
                <p class="ml-2 text-sm text-text-secondary">Enable or disable this webhook</p>
            </div>

            <!-- Actions -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                <div class="flex items-center space-x-3">
                    <a href="{% url 'webhooks:configuration' %}" 
                       class="px-6 py-3 border border-gray-300 text-text-primary rounded-lg hover:bg-gray-50 transition duration-150">
                        Cancel
                    </a>
                    <button type="button" onclick="testWebhook()" 
                            class="px-6 py-3 border border-primary text-primary rounded-lg hover:bg-primary/5 transition duration-150">
                        <i class="fa-solid fa-flask mr-2"></i>
                        Test Webhook
                    </button>
                </div>
                <button type="submit" 
                        class="px-6 py-3 bg-primary hover:bg-primary-hover text-white rounded-lg transition duration-150">
                    <i class="fa-solid fa-save mr-2"></i>
                    Update Webhook
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function testWebhook() {
    const webhookId = '{{ webhook_id }}';
    
    fetch(`/webhooks/${webhookId}/test/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Test webhook sent successfully!');
        } else {
            alert('Error testing webhook: ' + data.message);
        }
    })
    .catch(error => {
        alert('Error testing webhook: ' + error.message);
    });
}

document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('form');
    const submitBtn = form.querySelector('button[type="submit"]');
    
    form.addEventListener('submit', function(e) {
        const checkedEvents = form.querySelectorAll('input[name="events"]:checked');
        if (checkedEvents.length === 0) {
            e.preventDefault();
            alert('Please select at least one event to subscribe to.');
            return;
        }
        
        submitBtn.innerHTML = '<i class="fa-solid fa-spinner fa-spin mr-2"></i>Updating...';
        submitBtn.disabled = true;
    });
});
</script>
{% endblock %}
