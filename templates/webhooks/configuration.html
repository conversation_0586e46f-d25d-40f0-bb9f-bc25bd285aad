{% extends 'base.html' %}

{% block title %}Webhook Configuration - PDFlex{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-text-primary">Webhook Configuration</h1>
                <p class="mt-2 text-text-secondary">Configure webhooks to receive real-time notifications</p>
            </div>
            <div class="flex items-center space-x-3">
                <button id="test-webhook-btn" class="border border-gray-300 text-text-primary px-4 py-2 rounded-lg hover:bg-gray-50 transition duration-150">
                    <i class="fa-solid fa-flask mr-2"></i>
                    Test Webhook
                </button>
                <button id="add-webhook-btn" class="bg-primary hover:bg-primary-hover text-white px-4 py-2 rounded-lg transition duration-150">
                    <i class="fa-solid fa-plus mr-2"></i>
                    Add Webhook
                </button>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Webhook List -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-semibold text-text-primary">Active Webhooks</h2>
                        <span class="px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">{{ webhooks.count|default:2 }} configured</span>
                    </div>
                </div>
                
                <div class="divide-y divide-gray-200">
                    <!-- Webhook Item 1 -->
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                    <i class="fa-solid fa-webhook text-green-600"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-text-primary">Form Submission Webhook</h3>
                                    <p class="text-sm text-text-secondary">https://api.example.com/webhooks/form-submit</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Active</span>
                                <div class="relative">
                                    <button class="text-text-secondary hover:text-text-primary" onclick="toggleDropdown(this)">
                                        <i class="fa-solid fa-ellipsis-v"></i>
                                    </button>
                                    <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden dropdown-menu">
                                        <a href="#" class="block px-4 py-2 text-sm text-text-primary hover:bg-gray-100">Edit</a>
                                        <a href="#" class="block px-4 py-2 text-sm text-text-primary hover:bg-gray-100">Test</a>
                                        <a href="#" class="block px-4 py-2 text-sm text-text-primary hover:bg-gray-100">View Logs</a>
                                        <hr class="my-1">
                                        <a href="#" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100">Delete</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div>
                                <p class="text-sm font-medium text-text-primary">Events</p>
                                <div class="flex flex-wrap gap-1 mt-1">
                                    <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">form.submitted</span>
                                    <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">pdf.generated</span>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-text-primary">Last Triggered</p>
                                <p class="text-sm text-text-secondary mt-1">2 hours ago</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between text-sm">
                            <div class="flex items-center space-x-4">
                                <span class="text-text-secondary">Success Rate: <span class="text-green-600 font-medium">98.5%</span></span>
                                <span class="text-text-secondary">Last 24h: <span class="font-medium">47 calls</span></span>
                            </div>
                            <button class="text-primary hover:text-primary-hover">View Details</button>
                        </div>
                    </div>

                    <!-- Webhook Item 2 -->
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                                    <i class="fa-solid fa-webhook text-orange-600"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-text-primary">Error Notification</h3>
                                    <p class="text-sm text-text-secondary">https://hooks.slack.com/services/...</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">Paused</span>
                                <div class="relative">
                                    <button class="text-text-secondary hover:text-text-primary" onclick="toggleDropdown(this)">
                                        <i class="fa-solid fa-ellipsis-v"></i>
                                    </button>
                                    <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden dropdown-menu">
                                        <a href="#" class="block px-4 py-2 text-sm text-text-primary hover:bg-gray-100">Edit</a>
                                        <a href="#" class="block px-4 py-2 text-sm text-text-primary hover:bg-gray-100">Resume</a>
                                        <a href="#" class="block px-4 py-2 text-sm text-text-primary hover:bg-gray-100">View Logs</a>
                                        <hr class="my-1">
                                        <a href="#" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100">Delete</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div>
                                <p class="text-sm font-medium text-text-primary">Events</p>
                                <div class="flex flex-wrap gap-1 mt-1">
                                    <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded">error.occurred</span>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-text-primary">Last Triggered</p>
                                <p class="text-sm text-text-secondary mt-1">1 day ago</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between text-sm">
                            <div class="flex items-center space-x-4">
                                <span class="text-text-secondary">Success Rate: <span class="text-green-600 font-medium">100%</span></span>
                                <span class="text-text-secondary">Last 24h: <span class="font-medium">0 calls</span></span>
                            </div>
                            <button class="text-primary hover:text-primary-hover">View Details</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Configuration Panel -->
        <div class="lg:col-span-1">
            <div class="space-y-6">
                <!-- Quick Setup -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-text-primary mb-4">Quick Setup</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-text-primary mb-2">Webhook URL</label>
                            <input type="url" placeholder="https://your-app.com/webhook" 
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-text-primary mb-2">Events</label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" class="text-primary focus:ring-primary" checked>
                                    <span class="ml-2 text-sm">Form Submitted</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="text-primary focus:ring-primary">
                                    <span class="ml-2 text-sm">PDF Generated</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="text-primary focus:ring-primary">
                                    <span class="ml-2 text-sm">Error Occurred</span>
                                </label>
                            </div>
                        </div>
                        
                        <button class="w-full bg-primary hover:bg-primary-hover text-white px-4 py-2 rounded-lg transition duration-150">
                            Create Webhook
                        </button>
                    </div>
                </div>

                <!-- Documentation -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-blue-900 mb-4">
                        <i class="fa-solid fa-book mr-2"></i>
                        Documentation
                    </h3>
                    
                    <div class="space-y-3">
                        <a href="{% url 'api:documentation' %}#webhooks" class="block text-sm text-blue-700 hover:text-blue-900">
                            <i class="fa-solid fa-arrow-right mr-2"></i>
                            Webhook Events Reference
                        </a>
                        <a href="{% url 'api:documentation' %}#webhooks" class="block text-sm text-blue-700 hover:text-blue-900">
                            <i class="fa-solid fa-arrow-right mr-2"></i>
                            Payload Examples
                        </a>
                        <a href="{% url 'api:documentation' %}#authentication" class="block text-sm text-blue-700 hover:text-blue-900">
                            <i class="fa-solid fa-arrow-right mr-2"></i>
                            Security & Verification
                        </a>
                        <a href="{% url 'api:documentation' %}#errors" class="block text-sm text-blue-700 hover:text-blue-900">
                            <i class="fa-solid fa-arrow-right mr-2"></i>
                            Troubleshooting Guide
                        </a>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-text-primary mb-4">Recent Activity</h3>
                    
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                            <div class="flex-1">
                                <p class="text-sm text-text-primary">Webhook delivered successfully</p>
                                <p class="text-xs text-text-secondary">2 minutes ago</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                            <div class="flex-1">
                                <p class="text-sm text-text-primary">Form submission webhook triggered</p>
                                <p class="text-xs text-text-secondary">15 minutes ago</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                            <div class="flex-1">
                                <p class="text-sm text-text-primary">Webhook delivery failed (retry #2)</p>
                                <p class="text-xs text-text-secondary">1 hour ago</p>
                            </div>
                        </div>
                    </div>
                    
                    <button class="w-full mt-4 text-sm text-primary hover:text-primary-hover">
                        View All Activity
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Webhook Modal -->
<div id="testWebhookModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-text-primary">Test Webhook</h3>
            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeTestModal()">
                <i class="fa-solid fa-times"></i>
            </button>
        </div>
        
        <div class="mb-4">
            <label class="block text-sm font-medium text-text-primary mb-2">Select Event Type</label>
            <select class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary">
                <option>form.submitted</option>
                <option>pdf.generated</option>
                <option>error.occurred</option>
            </select>
        </div>
        
        <div class="mb-6">
            <label class="block text-sm font-medium text-text-primary mb-2">Test Payload</label>
            <textarea rows="4" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                      placeholder='{"event": "form.submitted", "data": {}}'></textarea>
        </div>
        
        <div class="flex justify-end space-x-3">
            <button onclick="closeTestModal()" class="px-4 py-2 border border-gray-300 text-text-primary rounded-lg hover:bg-gray-50 transition duration-150">
                Cancel
            </button>
            <button onclick="sendTestWebhook()" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition duration-150">
                Send Test
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Dropdown functionality
    function toggleDropdown(button) {
        const dropdown = button.nextElementSibling;
        const isHidden = dropdown.classList.contains('hidden');
        
        // Close all other dropdowns
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            menu.classList.add('hidden');
        });
        
        // Toggle current dropdown
        if (isHidden) {
            dropdown.classList.remove('hidden');
        }
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest('button')) {
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.classList.add('hidden');
            });
        }
    });

    // Test webhook modal
    function closeTestModal() {
        document.getElementById('testWebhookModal').classList.add('hidden');
    }

    function sendTestWebhook() {
        // Simulate sending test webhook
        alert('Test webhook sent! Check your endpoint for the test payload.');
        closeTestModal();
    }

    // Event listeners
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('test-webhook-btn').addEventListener('click', function() {
            document.getElementById('testWebhookModal').classList.remove('hidden');
        });
        
        document.getElementById('add-webhook-btn').addEventListener('click', function() {
            // In a real implementation, this would open a form or navigate to a creation page
            alert('Add webhook functionality would be implemented here');
        });
    });
</script>
{% endblock %}
