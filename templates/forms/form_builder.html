{% extends 'base.html' %}
{% load static %}

{% block title %}
  Form Builder - PDFlex
{% endblock %}

{% block content %}
  <div class="h-screen flex flex-col">
    <!-- Header -->
    <header class="bg-white border-b border-gray-200">
      <div class="flex items-center justify-between px-6 py-3">
        <div class="flex items-center space-x-4">
          <a href="{% url 'forms:form_list' %}" class="flex items-center space-x-2 cursor-pointer">
            <i class="fa-solid fa-arrow-left text-text-secondary hover:text-primary"></i>
            <span class="text-text-secondary hover:text-primary">Back to Forms</span>
          </a>
          <div class="w-px h-6 bg-gray-300"></div>
          <div>
            <h1 class="font-bold text-lg text-text-primary" contenteditable="true">Contact Information Form</h1>
            <p class="text-sm text-text-secondary">Last saved 2 minutes ago</p>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <button id="undo-btn" class="p-2 text-text-secondary hover:text-primary rounded-md hover:bg-gray-100" title="Undo">
              <i class="fa-solid fa-undo"></i>
            </button>
            <button id="redo-btn" class="p-2 text-text-secondary hover:text-primary rounded-md hover:bg-gray-100" title="Redo">
              <i class="fa-solid fa-redo"></i>
            </button>
            <div class="w-px h-6 bg-gray-300"></div>
            <button id="preview-btn" class="px-4 py-2 bg-gray-100 text-text-primary rounded-md hover:bg-gray-200">
              <i class="fa-solid fa-eye mr-2"></i>Preview
            </button>
            <button id="save-btn" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-hover">
              <i class="fa-solid fa-save mr-2"></i>Save
            </button>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
            <span class="text-xs text-text-secondary">Auto-saved</span>
          </div>
        </div>
      </div>
    </header>

    <div class="flex h-full">
      <!-- Component Palette -->
      <div class="w-80 bg-white border-r border-gray-200 overflow-y-auto">
        <div class="p-4">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-semibold text-text-primary">Components</h2>
            <button class="text-text-secondary hover:text-primary">
              <i class="fa-solid fa-grip-vertical"></i>
            </button>
          </div>
          
          <div class="space-y-4">
            <!-- Input Fields -->
            <div class="border border-gray-200 rounded-lg p-3">
              <h3 class="text-sm font-medium text-text-primary mb-3 flex items-center">
                <i class="fa-solid fa-keyboard mr-2 text-blue-500"></i>
                Input Fields
              </h3>
              <div class="space-y-2">
                <div class="draggable-element bg-white border border-gray-200 rounded-md p-3 cursor-move hover:shadow-md" draggable="true" data-type="text">
                  <div class="flex items-center space-x-2">
                    <i class="fa-solid fa-font text-blue-500"></i>
                    <span class="text-sm">Text Input</span>
                  </div>
                </div>
                <div class="draggable-element bg-white border border-gray-200 rounded-md p-3 cursor-move hover:shadow-md" draggable="true" data-type="textarea">
                  <div class="flex items-center space-x-2">
                    <i class="fa-solid fa-align-left text-amber-500"></i>
                    <span class="text-sm">Multi-line Text</span>
                  </div>
                </div>
                <div class="draggable-element bg-white border border-gray-200 rounded-md p-3 cursor-move hover:shadow-md" draggable="true" data-type="email">
                  <div class="flex items-center space-x-2">
                    <i class="fa-solid fa-envelope text-green-500"></i>
                    <span class="text-sm">Email</span>
                  </div>
                </div>
                <div class="draggable-element bg-white border border-gray-200 rounded-md p-3 cursor-move hover:shadow-md" draggable="true" data-type="password">
                  <div class="flex items-center space-x-2">
                    <i class="fa-solid fa-lock text-red-500"></i>
                    <span class="text-sm">Password</span>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Selection -->
            <div class="border border-gray-200 rounded-lg p-3">
              <h3 class="text-sm font-medium text-text-primary mb-3 flex items-center">
                <i class="fa-solid fa-list-check mr-2 text-orange-500"></i>
                Selection
              </h3>
              <div class="space-y-2">
                <div class="draggable-element bg-white border border-gray-200 rounded-md p-3 cursor-move hover:shadow-md" draggable="true" data-type="select">
                  <div class="flex items-center space-x-2">
                    <i class="fa-solid fa-caret-down text-orange-500"></i>
                    <span class="text-sm">Dropdown</span>
                  </div>
                </div>
                <div class="draggable-element bg-white border border-gray-200 rounded-md p-3 cursor-move hover:shadow-md" draggable="true" data-type="radio">
                  <div class="flex items-center space-x-2">
                    <i class="fa-solid fa-circle-dot text-pink-500"></i>
                    <span class="text-sm">Radio Buttons</span>
                  </div>
                </div>
                <div class="draggable-element bg-white border border-gray-200 rounded-md p-3 cursor-move hover:shadow-md" draggable="true" data-type="checkbox">
                  <div class="flex items-center space-x-2">
                    <i class="fa-solid fa-square-check text-emerald-500"></i>
                    <span class="text-sm">Checkboxes</span>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Date & Time -->
            <div class="border border-gray-200 rounded-lg p-3">
              <h3 class="text-sm font-medium text-text-primary mb-3 flex items-center">
                <i class="fa-solid fa-calendar-days mr-2 text-teal-500"></i>
                Date & Time
              </h3>
              <div class="space-y-2">
                <div class="draggable-element bg-white border border-gray-200 rounded-md p-3 cursor-move hover:shadow-md" draggable="true" data-type="date">
                  <div class="flex items-center space-x-2">
                    <i class="fa-solid fa-calendar text-rose-500"></i>
                    <span class="text-sm">Date Picker</span>
                  </div>
                </div>
                <div class="draggable-element bg-white border border-gray-200 rounded-md p-3 cursor-move hover:shadow-md" draggable="true" data-type="time">
                  <div class="flex items-center space-x-2">
                    <i class="fa-solid fa-clock text-violet-500"></i>
                    <span class="text-sm">Time Picker</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Canvas Area -->
      <div class="flex-1 bg-gray-100 p-6 overflow-y-auto">
        <div class="max-w-4xl mx-auto">
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="border-b border-gray-200 p-4">
              <div class="flex items-center justify-between">
                <div>
                  <h1 class="text-xl font-bold text-text-primary" contenteditable="true">Contact Information Form</h1>
                  <p class="text-sm text-text-secondary mt-1" contenteditable="true">Please fill out all required fields</p>
                </div>
                <div class="flex items-center space-x-2">
                  <span class="text-xs text-text-secondary">Real-time collaboration</span>
                  <div class="flex -space-x-2">
                    <div class="w-6 h-6 rounded-full bg-blue-500 border-2 border-white flex items-center justify-center text-white text-xs">A</div>
                    <div class="w-6 h-6 rounded-full bg-green-500 border-2 border-white flex items-center justify-center text-white text-xs">B</div>
                  </div>
                </div>
              </div>
            </div>
            
            <div id="form-canvas" class="p-6 space-y-6">
              <div class="drop-zone rounded-lg p-8 flex flex-col items-center justify-center text-text-secondary border-2 border-dashed border-gray-300">
                <i class="fa-solid fa-plus text-2xl mb-2"></i>
                <span class="text-sm font-medium">Drag components here to build your form</span>
                <span class="text-xs">Or click on any component from the left panel</span>
              </div>
              
              <!-- Sample form elements -->
              <div class="form-element p-4 rounded-lg relative group border-2 border-transparent hover:border-primary" data-id="1">
                <div class="element-controls absolute top-2 right-2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <button class="p-1 text-text-secondary hover:text-primary bg-white rounded shadow-sm">
                    <i class="fa-solid fa-grip-vertical text-xs"></i>
                  </button>
                  <button class="p-1 text-text-secondary hover:text-primary bg-white rounded shadow-sm">
                    <i class="fa-solid fa-copy text-xs"></i>
                  </button>
                  <button class="p-1 text-text-secondary hover:text-red-500 bg-white rounded shadow-sm">
                    <i class="fa-solid fa-trash text-xs"></i>
                  </button>
                </div>
                <label class="block text-sm font-medium text-text-primary mb-2">Full Name <span class="text-red-500">*</span></label>
                <input type="text" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary" placeholder="Enter your full name">
              </div>
              
              <div class="form-element p-4 rounded-lg relative group border-2 border-transparent hover:border-primary" data-id="2">
                <div class="element-controls absolute top-2 right-2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <button class="p-1 text-text-secondary hover:text-primary bg-white rounded shadow-sm">
                    <i class="fa-solid fa-grip-vertical text-xs"></i>
                  </button>
                  <button class="p-1 text-text-secondary hover:text-primary bg-white rounded shadow-sm">
                    <i class="fa-solid fa-copy text-xs"></i>
                  </button>
                  <button class="p-1 text-text-secondary hover:text-red-500 bg-white rounded shadow-sm">
                    <i class="fa-solid fa-trash text-xs"></i>
                  </button>
                </div>
                <label class="block text-sm font-medium text-text-primary mb-2">Email Address <span class="text-red-500">*</span></label>
                <input type="email" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary" placeholder="Enter your email address">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script type="module" src="{% static 'dist/js/main-C3cVkHpJ.js' %}"></script>
  <script>
    // Form builder functionality
    document.addEventListener('DOMContentLoaded', function() {
      // Drag and drop functionality
      const draggableElements = document.querySelectorAll('.draggable-element');
      const dropZone = document.querySelector('.drop-zone');
      const formCanvas = document.getElementById('form-canvas');
      
      draggableElements.forEach(element => {
        element.addEventListener('dragstart', function(e) {
          e.dataTransfer.setData('text/plain', this.dataset.type);
        });
      });
      
      dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('border-primary', 'bg-blue-50');
      });
      
      dropZone.addEventListener('dragleave', function(e) {
        this.classList.remove('border-primary', 'bg-blue-50');
      });
      
      dropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        const elementType = e.dataTransfer.getData('text/plain');
        console.log('Dropped element type:', elementType);
        this.classList.remove('border-primary', 'bg-blue-50');
        // Add logic to create form element
      });
      
      // Save functionality
      document.getElementById('save-btn').addEventListener('click', function() {
        console.log('Saving form...');
        // Add save logic here
      });
      
      // Preview functionality
      document.getElementById('preview-btn').addEventListener('click', function() {
        console.log('Opening preview...');
        // Add preview logic here
      });
    });
  </script>
{% endblock %}
