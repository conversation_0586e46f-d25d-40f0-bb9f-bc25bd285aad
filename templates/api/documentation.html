{% extends 'base.html' %}

{% block title %}API Documentation - PDFlex{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-text-primary">API Documentation</h1>
                <p class="mt-2 text-text-secondary">Complete reference for the PDFlex REST API</p>
            </div>
            <div class="flex items-center space-x-3">
                <span class="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">v1.0</span>
                <a href="#" class="bg-primary hover:bg-primary-hover text-white px-4 py-2 rounded-lg transition duration-150">
                    <i class="fa-solid fa-download mr-2"></i>
                    Download OpenAPI Spec
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Sidebar Navigation -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-8">
                <h3 class="font-semibold text-text-primary mb-4">Quick Navigation</h3>
                <nav class="space-y-2">
                    <a href="#authentication" class="block text-sm text-text-secondary hover:text-primary transition duration-150">Authentication</a>
                    <a href="#forms" class="block text-sm text-text-secondary hover:text-primary transition duration-150">Forms API</a>
                    <a href="#templates" class="block text-sm text-text-secondary hover:text-primary transition duration-150">Templates API</a>
                    <a href="#generation" class="block text-sm text-text-secondary hover:text-primary transition duration-150">PDF Generation</a>
                    <a href="#webhooks" class="block text-sm text-text-secondary hover:text-primary transition duration-150">Webhooks</a>
                    <a href="#errors" class="block text-sm text-text-secondary hover:text-primary transition duration-150">Error Codes</a>
                </nav>
                
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <h4 class="font-medium text-text-primary mb-3">SDKs & Libraries</h4>
                    <div class="space-y-2">
                        <a href="#" class="flex items-center text-sm text-text-secondary hover:text-primary">
                            <i class="fa-brands fa-python mr-2"></i> Python SDK
                        </a>
                        <a href="#" class="flex items-center text-sm text-text-secondary hover:text-primary">
                            <i class="fa-brands fa-js mr-2"></i> JavaScript SDK
                        </a>
                        <a href="#" class="flex items-center text-sm text-text-secondary hover:text-primary">
                            <i class="fa-brands fa-php mr-2"></i> PHP SDK
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="lg:col-span-3">
            <!-- Getting Started -->
            <section class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
                <h2 class="text-2xl font-bold text-text-primary mb-4">Getting Started</h2>
                <p class="text-text-secondary mb-4">
                    The PDFlex API allows you to programmatically create forms, generate PDFs, and manage templates. 
                    All API endpoints are RESTful and return JSON responses.
                </p>
                
                <div class="bg-gray-50 rounded-lg p-4 mb-4">
                    <h3 class="font-medium text-text-primary mb-2">Base URL</h3>
                    <code class="text-sm bg-gray-100 px-2 py-1 rounded">https://api.pdflex.com/v1</code>
                </div>
                
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-start space-x-3">
                        <i class="fa-solid fa-info-circle text-blue-500 text-xl"></i>
                        <div>
                            <h3 class="font-medium text-blue-900 mb-1">Rate Limiting</h3>
                            <p class="text-sm text-blue-700">API requests are limited to 1000 requests per hour per API key.</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Authentication -->
            <section id="authentication" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
                <h2 class="text-2xl font-bold text-text-primary mb-4">Authentication</h2>
                <p class="text-text-secondary mb-4">
                    PDFlex API uses API keys for authentication. Include your API key in the Authorization header.
                </p>
                
                <div class="bg-gray-900 rounded-lg p-4 mb-4">
                    <pre class="text-green-400 text-sm"><code>curl -H "Authorization: Bearer YOUR_API_KEY" \
     -H "Content-Type: application/json" \
     https://api.pdflex.com/v1/forms</code></pre>
                </div>
                
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex items-start space-x-3">
                        <i class="fa-solid fa-exclamation-triangle text-yellow-500 text-xl"></i>
                        <div>
                            <h3 class="font-medium text-yellow-900 mb-1">Keep your API key secure</h3>
                            <p class="text-sm text-yellow-700">Never expose your API key in client-side code or public repositories.</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Forms API -->
            <section id="forms" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
                <h2 class="text-2xl font-bold text-text-primary mb-4">Forms API</h2>
                
                <div class="space-y-6">
                    <!-- List Forms -->
                    <div class="border-l-4 border-blue-500 pl-4">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-semibold text-text-primary">List Forms</h3>
                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">GET</span>
                        </div>
                        <p class="text-sm text-text-secondary mb-3">Retrieve a list of all forms</p>
                        <div class="bg-gray-900 rounded p-3">
                            <code class="text-green-400 text-sm">GET /v1/forms</code>
                        </div>
                    </div>

                    <!-- Create Form -->
                    <div class="border-l-4 border-orange-500 pl-4">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-semibold text-text-primary">Create Form</h3>
                            <span class="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded">POST</span>
                        </div>
                        <p class="text-sm text-text-secondary mb-3">Create a new form</p>
                        <div class="bg-gray-900 rounded p-3 mb-3">
                            <code class="text-green-400 text-sm">POST /v1/forms</code>
                        </div>
                        <div class="bg-gray-50 rounded p-3">
                            <h4 class="font-medium text-text-primary mb-2">Request Body</h4>
                            <pre class="text-sm"><code>{
  "name": "Contact Form",
  "description": "Customer contact form",
  "fields": [
    {
      "type": "text",
      "name": "name",
      "label": "Full Name",
      "required": true
    }
  ]
}</code></pre>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Templates API -->
            <section id="templates" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
                <h2 class="text-2xl font-bold text-text-primary mb-4">Templates API</h2>
                
                <div class="space-y-6">
                    <!-- List Templates -->
                    <div class="border-l-4 border-purple-500 pl-4">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-semibold text-text-primary">List Templates</h3>
                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">GET</span>
                        </div>
                        <p class="text-sm text-text-secondary mb-3">Retrieve a list of PDF templates</p>
                        <div class="bg-gray-900 rounded p-3">
                            <code class="text-green-400 text-sm">GET /v1/templates</code>
                        </div>
                    </div>

                    <!-- Generate PDF -->
                    <div class="border-l-4 border-red-500 pl-4">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-semibold text-text-primary">Generate PDF</h3>
                            <span class="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded">POST</span>
                        </div>
                        <p class="text-sm text-text-secondary mb-3">Generate a PDF from a template</p>
                        <div class="bg-gray-900 rounded p-3">
                            <code class="text-green-400 text-sm">POST /v1/templates/{id}/generate</code>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Error Codes -->
            <section id="errors" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 class="text-2xl font-bold text-text-primary mb-4">Error Codes</h2>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">400</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Bad Request - Invalid parameters</td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">401</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Unauthorized - Invalid API key</td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">404</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Not Found - Resource not found</td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">429</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Too Many Requests - Rate limit exceeded</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Smooth scrolling for navigation links
    document.addEventListener('DOMContentLoaded', function() {
        const navLinks = document.querySelectorAll('a[href^="#"]');
        
        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    });
</script>
{% endblock %}
