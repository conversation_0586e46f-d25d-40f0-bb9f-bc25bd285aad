{% extends 'base.html' %}
{% load crispy_forms_tags %}
{% load oauth_tags %}

{% block title %}
  Login - PDFlex
{% endblock %}

{% block content %}
  <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary/10">
          <i class="fa-solid fa-sign-in-alt text-primary text-xl"></i>
        </div>
        <h2 class="mt-6 text-center text-3xl font-bold text-text-primary">Welcome back</h2>
        <p class="mt-2 text-center text-sm text-text-secondary">Sign in to your PDFlex account</p>
      </div>

      <div class="bg-white py-8 px-6 shadow-lg rounded-lg border border-gray-200">
        <!-- Social Login -->
        {% oauth_buttons %}

        <!-- Login Form -->
        <form method="post" class="space-y-6">
          {% csrf_token %}

          <div>
            <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-text-primary mb-2"><i class="fa-solid fa-user mr-1"></i> Username or Email</label>
            <input type="text" name="{{ form.username.name }}" id="{{ form.username.id_for_label }}" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition duration-150" placeholder="Enter your username or email" required />
            {% if form.username.errors %}
              <p class="mt-1 text-sm text-red-600">{{ form.username.errors.0 }}</p>
            {% endif %}
          </div>

          <div>
            <label for="{{ form.password.id_for_label }}" class="block text-sm font-medium text-text-primary mb-2"><i class="fa-solid fa-lock mr-1"></i> Password</label>
            <input type="password" name="{{ form.password.name }}" id="{{ form.password.id_for_label }}" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition duration-150" placeholder="Enter your password" required />
            {% if form.password.errors %}
              <p class="mt-1 text-sm text-red-600">{{ form.password.errors.0 }}</p>
            {% endif %}
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <input type="checkbox" name="{{ form.remember_me.name }}" id="{{ form.remember_me.id_for_label }}" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded" />
              <label for="{{ form.remember_me.id_for_label }}" class="ml-2 block text-sm text-text-secondary">Remember me</label>
            </div>
            <div class="text-sm">
              <a href="{% url 'users:password_reset' %}" class="text-primary hover:text-primary-hover transition duration-150">Forgot your password?</a>
            </div>
          </div>

          {% if form.non_field_errors %}
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">{{ form.non_field_errors.0 }}</div>
          {% endif %}

          <button type="submit" class="w-full bg-primary hover:bg-primary-hover text-white font-semibold py-3 px-4 rounded-lg transition duration-150 transform hover:-translate-y-0.5">
            <i class="fa-solid fa-sign-in-alt mr-2"></i>
            Sign In
          </button>
        </form>

        <div class="mt-6 text-center">
          <p class="text-sm text-text-secondary">
            Don't have an account?
            <a href="{% url 'users:register' %}" class="text-primary hover:text-primary-hover font-medium transition duration-150">Sign up here</a>
          </p>
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script>
    // Form validation and enhancement
    document.addEventListener('DOMContentLoaded', function () {
      const form = document.querySelector('form')
      const submitBtn = form.querySelector('button[type="submit"]')
    
      form.addEventListener('submit', function () {
        submitBtn.innerHTML = '<i class="fa-solid fa-spinner fa-spin mr-2"></i>Signing in...'
        submitBtn.disabled = true
      })
    })
  </script>
{% endblock %}
