{% extends 'base.html' %}

{% block title %}Reset Password - PDFlex{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div class="text-center">
            <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary/10">
                <i class="fa-solid fa-key text-primary text-xl"></i>
            </div>
            <h2 class="mt-6 text-center text-3xl font-bold text-text-primary">Reset Your Password</h2>
            <p class="mt-2 text-center text-sm text-text-secondary">Enter your new password below</p>
        </div>

        <div class="bg-white py-8 px-6 shadow-lg rounded-lg border border-gray-200">
            {% if validlink %}
                <form method="post" class="space-y-6">
                    {% csrf_token %}
                    
                    <div>
                        <label for="{{ form.new_password1.id_for_label }}" class="block text-sm font-medium text-text-primary mb-2">
                            <i class="fa-solid fa-lock mr-1"></i>
                            New Password
                        </label>
                        <input type="password" name="{{ form.new_password1.name }}" id="{{ form.new_password1.id_for_label }}" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition duration-150" 
                               placeholder="Enter your new password" required>
                        {% if form.new_password1.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {% for error in form.new_password1.errors %}
                                    <p>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div>
                        <label for="{{ form.new_password2.id_for_label }}" class="block text-sm font-medium text-text-primary mb-2">
                            <i class="fa-solid fa-lock mr-1"></i>
                            Confirm New Password
                        </label>
                        <input type="password" name="{{ form.new_password2.name }}" id="{{ form.new_password2.id_for_label }}" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition duration-150" 
                               placeholder="Confirm your new password" required>
                        {% if form.new_password2.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {% for error in form.new_password2.errors %}
                                    <p>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    {% if form.non_field_errors %}
                        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                            {% for error in form.non_field_errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}

                    <!-- Password Requirements -->
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <p class="text-sm font-medium text-text-primary mb-2">Password Requirements:</p>
                        <ul class="text-sm text-text-secondary space-y-1">
                            <li>• At least 8 characters long</li>
                            <li>• Cannot be too similar to your personal information</li>
                            <li>• Cannot be a commonly used password</li>
                            <li>• Cannot be entirely numeric</li>
                        </ul>
                    </div>

                    <button type="submit" class="w-full bg-primary hover:bg-primary-hover text-white font-semibold py-3 px-4 rounded-lg transition duration-150 transform hover:-translate-y-0.5">
                        <i class="fa-solid fa-save mr-2"></i>
                        Reset Password
                    </button>
                </form>
            {% else %}
                <div class="text-center">
                    <div class="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-red-100 mb-6">
                        <i class="fa-solid fa-exclamation-triangle text-red-600 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-text-primary mb-4">Invalid Reset Link</h3>
                    <p class="text-text-secondary mb-6">
                        This password reset link is invalid or has expired. 
                        Please request a new password reset.
                    </p>
                    <a href="{% url 'users:password_reset' %}" 
                       class="bg-primary hover:bg-primary-hover text-white px-6 py-3 rounded-lg transition duration-150 inline-flex items-center">
                        <i class="fa-solid fa-redo mr-2"></i>
                        Request New Reset Link
                    </a>
                </div>
            {% endif %}
        </div>

        <div class="text-center">
            <p class="text-sm text-text-secondary">
                Remember your password?
                <a href="{% url 'users:login' %}" class="text-primary hover:text-primary-hover font-medium transition duration-150">
                    Sign in here
                </a>
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    if (form) {
        const submitBtn = form.querySelector('button[type="submit"]');
        
        form.addEventListener('submit', function() {
            submitBtn.innerHTML = '<i class="fa-solid fa-spinner fa-spin mr-2"></i>Resetting Password...';
            submitBtn.disabled = true;
        });

        // Password strength indicator
        const password1 = document.getElementById('{{ form.new_password1.id_for_label }}');
        const password2 = document.getElementById('{{ form.new_password2.id_for_label }}');
        
        if (password1 && password2) {
            password2.addEventListener('input', function() {
                if (password1.value && password2.value) {
                    if (password1.value === password2.value) {
                        password2.classList.remove('border-red-300');
                        password2.classList.add('border-green-300');
                    } else {
                        password2.classList.remove('border-green-300');
                        password2.classList.add('border-red-300');
                    }
                }
            });
        }
    }
});
</script>
{% endblock %}
