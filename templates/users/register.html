{% extends 'base.html' %}
{% load crispy_forms_tags %}
{% load oauth_tags %}

{% block title %}
  Register - PDFlex
{% endblock %}

{% block content %}
  <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary/10">
          <i class="fa-solid fa-user-plus text-primary text-xl"></i>
        </div>
        <h2 class="mt-6 text-center text-3xl font-bold text-text-primary">Create your account</h2>
        <p class="mt-2 text-center text-sm text-text-secondary">Join PDFlex and start creating amazing forms</p>
      </div>

      <div class="bg-white py-8 px-6 shadow-lg rounded-lg border border-gray-200">
        <!-- Social Login -->
        {% oauth_buttons %}

        <!-- Registration Form -->
        <form method="post" class="space-y-6">
          {% csrf_token %}

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-text-primary mb-2"><i class="fa-solid fa-user mr-1"></i> First Name</label>
              <input type="text" name="{{ form.first_name.name }}" id="{{ form.first_name.id_for_label }}" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition duration-150" placeholder="First name" required />
              {% if form.first_name.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.first_name.errors.0 }}</p>
              {% endif %}
            </div>

            <div>
              <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-text-primary mb-2"><i class="fa-solid fa-user mr-1"></i> Last Name</label>
              <input type="text" name="{{ form.last_name.name }}" id="{{ form.last_name.id_for_label }}" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition duration-150" placeholder="Last name" required />
              {% if form.last_name.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.last_name.errors.0 }}</p>
              {% endif %}
            </div>
          </div>

          <div>
            <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-text-primary mb-2"><i class="fa-solid fa-at mr-1"></i> Username</label>
            <input type="text" name="{{ form.username.name }}" id="{{ form.username.id_for_label }}" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition duration-150" placeholder="Choose a username" required />
            {% if form.username.errors %}
              <p class="mt-1 text-sm text-red-600">{{ form.username.errors.0 }}</p>
            {% endif %}
          </div>

          <div>
            <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-text-primary mb-2"><i class="fa-solid fa-envelope mr-1"></i> Email Address</label>
            <input type="email" name="{{ form.email.name }}" id="{{ form.email.id_for_label }}" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition duration-150" placeholder="Enter your email" required />
            {% if form.email.errors %}
              <p class="mt-1 text-sm text-red-600">{{ form.email.errors.0 }}</p>
            {% endif %}
          </div>

          <div>
            <label for="{{ form.password1.id_for_label }}" class="block text-sm font-medium text-text-primary mb-2"><i class="fa-solid fa-lock mr-1"></i> Password</label>
            <input type="password" name="{{ form.password1.name }}" id="{{ form.password1.id_for_label }}" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition duration-150" placeholder="Create a password" required />
            {% if form.password1.errors %}
              <p class="mt-1 text-sm text-red-600">{{ form.password1.errors.0 }}</p>
            {% endif %}
          </div>

          <div>
            <label for="{{ form.password2.id_for_label }}" class="block text-sm font-medium text-text-primary mb-2"><i class="fa-solid fa-lock mr-1"></i> Confirm Password</label>
            <input type="password" name="{{ form.password2.name }}" id="{{ form.password2.id_for_label }}" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition duration-150" placeholder="Confirm your password" required />
            {% if form.password2.errors %}
              <p class="mt-1 text-sm text-red-600">{{ form.password2.errors.0 }}</p>
            {% endif %}
          </div>

          <div class="flex items-center">
            <input type="checkbox" id="terms" name="terms" required class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded" />
            <label for="terms" class="ml-2 block text-sm text-text-secondary">
              I agree to the <a href="#" class="text-primary hover:text-primary-hover">Terms of Service</a>
              and <a href="#" class="text-primary hover:text-primary-hover">Privacy Policy</a>
            </label>
          </div>

          {% if form.non_field_errors %}
            <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">{{ form.non_field_errors.0 }}</div>
          {% endif %}

          <button type="submit" class="w-full bg-primary hover:bg-primary-hover text-white font-semibold py-3 px-4 rounded-lg transition duration-150 transform hover:-translate-y-0.5">
            <i class="fa-solid fa-user-plus mr-2"></i>
            Create Account
          </button>
        </form>

        <div class="mt-6 text-center">
          <p class="text-sm text-text-secondary">
            Already have an account?
            <a href="{% url 'users:login' %}" class="text-primary hover:text-primary-hover font-medium transition duration-150">Sign in here</a>
          </p>
        </div>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script>
    // Form validation and enhancement
    document.addEventListener('DOMContentLoaded', function () {
      const form = document.querySelector('form')
      const submitBtn = form.querySelector('button[type="submit"]')
    
      form.addEventListener('submit', function () {
        submitBtn.innerHTML = '<i class="fa-solid fa-spinner fa-spin mr-2"></i>Creating account...'
        submitBtn.disabled = true
      })
    
      // Password strength indicator
      const password1 = document.getElementById('{{ form.password1.id_for_label }}')
      const password2 = document.getElementById('{{ form.password2.id_for_label }}')
    
      if (password2) {
        password2.addEventListener('input', function () {
          if (password1.value !== password2.value) {
            password2.setCustomValidity('Passwords do not match')
          } else {
            password2.setCustomValidity('')
          }
        })
      }
    })
  </script>
{% endblock %}
