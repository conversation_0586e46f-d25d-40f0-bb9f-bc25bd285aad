{% extends 'base.html' %}

{% block title %}
  Registration Complete - PDFlex
{% endblock %}

{% block content %}
  <div class="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full text-center">
      <div class="mb-8">
        <div class="mx-auto h-24 w-24 flex items-center justify-center rounded-full bg-blue-100 mb-6">
          <i class="fa-solid fa-envelope-circle-check text-blue-500 text-4xl"></i>
        </div>
        <h2 class="text-3xl font-bold text-text-primary mb-4">Check Your Email</h2>
        <p class="text-text-secondary mb-8">We've sent a verification link to your email address. Please click the link in the email to verify your account and complete the registration process.</p>
      </div>

      <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
        <div class="flex items-start space-x-3">
          <div class="flex-shrink-0">
            <i class="fa-solid fa-info-circle text-blue-500 text-xl"></i>
          </div>
          <div class="text-left">
            <h3 class="font-medium text-blue-900 mb-2">Didn't receive the email?</h3>
            <p class="text-sm text-blue-700 mb-3">Check your spam folder or wait a few minutes for the email to arrive.</p>
            <button onclick="resendVerification()" class="text-sm text-blue-600 hover:text-blue-800 underline">Click here to resend verification email</button>
          </div>
        </div>
      </div>

      <div class="space-y-4">
        <a href="{% url 'users:login' %}" class="w-full bg-primary hover:bg-primary-hover text-white px-6 py-3 rounded-lg transition duration-150 inline-flex items-center justify-center">
          <i class="fa-solid fa-sign-in-alt mr-2"></i>
          Go to Login
        </a>

        <a href="{% url 'core:home' %}" class="w-full border border-gray-300 text-text-primary px-6 py-3 rounded-lg hover:bg-gray-50 transition duration-150 inline-flex items-center justify-center">
          <i class="fa-solid fa-home mr-2"></i>
          Back to Home
        </a>
      </div>

      <div class="mt-8 pt-8 border-t border-gray-200">
        <p class="text-sm text-text-secondary">
          Need help? <a href="#" class="text-primary hover:text-primary-hover">Contact Support</a>
        </p>
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script>
    function resendVerification() {
      // Show loading state
      const button = event.target
      const originalText = button.textContent
      button.textContent = 'Sending...'
      button.disabled = true
    
      // Simulate API call (replace with actual implementation)
      setTimeout(() => {
        button.textContent = 'Email sent!'
        setTimeout(() => {
          button.textContent = originalText
          button.disabled = false
        }, 3000)
      }, 1000)
    }
  </script>
{% endblock %}
