{% extends 'base.html' %}

{% block title %}
  Dashboard - PDFlex
{% endblock %}

{% block content %}
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Welcome Header -->
    <div class="bg-gradient-to-r from-primary to-primary-hover rounded-lg p-8 mb-8 text-white">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold mb-2">Welcome back, {{ user.get_display_name }}!</h1>
          <p class="text-blue-100">Here's what's happening with your forms and templates today.</p>
        </div>
        <div class="hidden md:block">
          <div class="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center">
            <i class="fa-solid fa-user text-3xl"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-file-text text-blue-600 text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-2xl font-bold text-text-primary">{{ stats.total_forms|default:0 }}</p>
            <p class="text-text-secondary text-sm">Total Forms</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-file-pdf text-green-600 text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-2xl font-bold text-text-primary">{{ stats.total_templates|default:0 }}</p>
            <p class="text-text-secondary text-sm">PDF Templates</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-paper-plane text-purple-600 text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-2xl font-bold text-text-primary">{{ stats.total_submissions|default:0 }}</p>
            <p class="text-text-secondary text-sm">Submissions</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
            <i class="fa-solid fa-download text-orange-600 text-xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-2xl font-bold text-text-primary">{{ stats.total_downloads|default:0 }}</p>
            <p class="text-text-secondary text-sm">Downloads</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
      <h2 class="text-xl font-semibold text-text-primary mb-4">Quick Actions</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <a href="{% url 'forms:form_create' %}" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-150">
          <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
            <i class="fa-solid fa-plus text-blue-600"></i>
          </div>
          <div>
            <h3 class="font-medium text-text-primary">Create New Form</h3>
            <p class="text-sm text-text-secondary">Build a new form from scratch</p>
          </div>
        </a>

        <a href="{% url 'pdf_generation:template_list' %}" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-150">
          <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-4">
            <i class="fa-solid fa-file-pdf text-green-600"></i>
          </div>
          <div>
            <h3 class="font-medium text-text-primary">New PDF Template</h3>
            <p class="text-sm text-text-secondary">Design a PDF template</p>
          </div>
        </a>

        <a href="{% url 'code_generator:generator' %}" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-150">
          <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
            <i class="fa-solid fa-code text-purple-600"></i>
          </div>
          <div>
            <h3 class="font-medium text-text-primary">Generate Code</h3>
            <p class="text-sm text-text-secondary">Export code for your templates</p>
          </div>
        </a>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Recent Forms -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-6 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h2 class="text-xl font-semibold text-text-primary">Recent Forms</h2>
            <a href="{% url 'forms:form_list' %}" class="text-primary hover:text-primary-hover text-sm">View all</a>
          </div>
        </div>
        <div class="p-6">
          {% if recent_forms %}
            <div class="space-y-4">
              {% for form in recent_forms %}
                <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                      <i class="fa-solid fa-file-text text-blue-600 text-sm"></i>
                    </div>
                    <div>
                      <h3 class="font-medium text-text-primary">{{ form.name }}</h3>
                      <p class="text-sm text-text-secondary">{{ form.created_at|timesince }} ago</p>
                    </div>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Active</span>
                    <a href="{% url 'forms:form_detail' form.pk %}" class="text-text-secondary hover:text-primary"><i class="fa-solid fa-external-link-alt"></i></a>
                  </div>
                </div>
              {% endfor %}
            </div>
          {% else %}
            <div class="text-center py-8">
              <i class="fa-solid fa-file-text text-gray-400 text-3xl mb-3"></i>
              <p class="text-text-secondary">No forms created yet</p>
              <a href="{% url 'forms:form_create' %}" class="text-primary hover:text-primary-hover text-sm">Create your first form</a>
            </div>
          {% endif %}
        </div>
      </div>

      <!-- Recent Templates -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-6 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h2 class="text-xl font-semibold text-text-primary">Recent Templates</h2>
            <a href="{% url 'pdf_generation:template_list' %}" class="text-primary hover:text-primary-hover text-sm">View all</a>
          </div>
        </div>
        <div class="p-6">
          {% if recent_templates %}
            <div class="space-y-4">
              {% for template in recent_templates %}
                <div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                      <i class="fa-solid fa-file-pdf text-green-600 text-sm"></i>
                    </div>
                    <div>
                      <h3 class="font-medium text-text-primary">{{ template.name }}</h3>
                      <p class="text-sm text-text-secondary">{{ template.created_at|timesince }} ago</p>
                    </div>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">{{ template.status|title }}</span>
                    <a href="{% url 'pdf_generation:template_detail' template.pk %}" class="text-text-secondary hover:text-primary"><i class="fa-solid fa-external-link-alt"></i></a>
                  </div>
                </div>
              {% endfor %}
            </div>
          {% else %}
            <div class="text-center py-8">
              <i class="fa-solid fa-file-pdf text-gray-400 text-3xl mb-3"></i>
              <p class="text-text-secondary">No templates created yet</p>
              <a href="{% url 'pdf_generation:template_list' %}" class="text-primary hover:text-primary-hover text-sm">Create your first template</a>
            </div>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Activity Feed -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mt-8">
      <div class="p-6 border-b border-gray-200">
        <h2 class="text-xl font-semibold text-text-primary">Recent Activity</h2>
      </div>
      <div class="p-6">
        {% if recent_activity %}
          <div class="space-y-4">
            {% for activity in recent_activity %}
              <div class="flex items-start space-x-3">
                <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                  <i class="fa-solid fa-{{ activity.icon }} text-gray-600 text-sm"></i>
                </div>
                <div class="flex-1">
                  <p class="text-text-primary">{{ activity.description }}</p>
                  <p class="text-sm text-text-secondary">{{ activity.timestamp|timesince }} ago</p>
                </div>
              </div>
            {% endfor %}
          </div>
        {% else %}
          <div class="text-center py-8">
            <i class="fa-solid fa-clock text-gray-400 text-3xl mb-3"></i>
            <p class="text-text-secondary">No recent activity</p>
          </div>
        {% endif %}
      </div>
    </div>
  </div>
{% endblock %}

{% block extra_js %}
  <script>
    // Dashboard functionality
    document.addEventListener('DOMContentLoaded', function () {
      // Add any dashboard-specific JavaScript here
      console.log('Dashboard loaded')
    })
  </script>
{% endblock %}
