{% extends 'base.html' %}

{% block title %}Password Reset Complete - PDFlex{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div class="text-center">
            <div class="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-green-100 mb-6">
                <i class="fa-solid fa-check-circle text-green-600 text-2xl"></i>
            </div>
            <h2 class="text-3xl font-bold text-text-primary mb-4">Password Reset Complete</h2>
            <p class="text-text-secondary mb-8">
                Your password has been successfully reset. You can now sign in with your new password.
            </p>
        </div>

        <div class="bg-white py-8 px-6 shadow-lg rounded-lg border border-gray-200">
            <div class="text-center space-y-6">
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-center justify-center">
                        <i class="fa-solid fa-shield-alt text-green-600 mr-3"></i>
                        <div class="text-left">
                            <p class="text-sm text-green-800 font-medium">Security Tip</p>
                            <p class="text-sm text-green-700">
                                Keep your password secure and don't share it with anyone.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="space-y-4">
                    <a href="{% url 'users:login' %}" 
                       class="w-full bg-primary hover:bg-primary-hover text-white font-semibold py-3 px-4 rounded-lg transition duration-150 transform hover:-translate-y-0.5 inline-flex items-center justify-center">
                        <i class="fa-solid fa-sign-in-alt mr-2"></i>
                        Sign In Now
                    </a>
                    
                    <a href="{% url 'core:home' %}" 
                       class="w-full border border-gray-300 text-text-primary py-3 px-4 rounded-lg hover:bg-gray-50 transition duration-150 inline-flex items-center justify-center">
                        <i class="fa-solid fa-home mr-2"></i>
                        Go to Homepage
                    </a>
                </div>
            </div>
        </div>

        <div class="text-center">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p class="text-sm text-blue-800 font-medium mb-2">Need Help?</p>
                <div class="space-y-1 text-sm text-blue-700">
                    <p>
                        <a href="mailto:<EMAIL>" class="hover:underline">
                            <i class="fa-solid fa-envelope mr-1"></i>
                            Contact Support
                        </a>
                    </p>
                    <p>
                        <a href="{% url 'api:documentation' %}" class="hover:underline">
                            <i class="fa-solid fa-question-circle mr-1"></i>
                            Help Center
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-redirect to login after 10 seconds
    let countdown = 10;
    const redirectMessage = document.createElement('div');
    redirectMessage.className = 'text-center mt-4 text-sm text-text-secondary';
    redirectMessage.innerHTML = `<p>Redirecting to login in <span id="countdown">${countdown}</span> seconds...</p>`;
    
    // Add the message to the page
    const container = document.querySelector('.max-w-md');
    container.appendChild(redirectMessage);
    
    const countdownElement = document.getElementById('countdown');
    
    const timer = setInterval(function() {
        countdown--;
        countdownElement.textContent = countdown;
        
        if (countdown <= 0) {
            clearInterval(timer);
            window.location.href = "{% url 'users:login' %}";
        }
    }, 1000);
    
    // Allow user to cancel auto-redirect by clicking anywhere
    document.addEventListener('click', function() {
        clearInterval(timer);
        redirectMessage.remove();
    }, { once: true });
});
</script>
{% endblock %}
