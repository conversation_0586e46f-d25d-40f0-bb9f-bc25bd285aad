{% extends 'base.html' %}

{% block title %}Password Reset Sent - PDFlex{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div class="text-center">
            <div class="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-green-100 mb-6">
                <i class="fa-solid fa-envelope text-green-600 text-2xl"></i>
            </div>
            <h2 class="text-3xl font-bold text-text-primary mb-4">Check Your Email</h2>
            <p class="text-text-secondary mb-8">
                We've sent password reset instructions to your email address. 
                Please check your inbox and follow the link to reset your password.
            </p>
        </div>

        <div class="bg-white py-8 px-6 shadow-lg rounded-lg border border-gray-200">
            <div class="text-center space-y-4">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fa-solid fa-info-circle text-blue-500 mr-3"></i>
                        <div class="text-left">
                            <p class="text-sm text-blue-800 font-medium">What's next?</p>
                            <p class="text-sm text-blue-700">
                                Click the link in the email to create a new password. 
                                The link will expire in 24 hours for security.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="space-y-3">
                    <p class="text-sm text-text-secondary">Didn't receive the email?</p>
                    <ul class="text-sm text-text-secondary space-y-1">
                        <li>• Check your spam/junk folder</li>
                        <li>• Make sure you entered the correct email address</li>
                        <li>• Wait a few minutes for the email to arrive</li>
                    </ul>
                </div>

                <div class="pt-4 border-t border-gray-200">
                    <a href="{% url 'users:password_reset' %}" 
                       class="text-primary hover:text-primary-hover font-medium transition duration-150">
                        <i class="fa-solid fa-redo mr-1"></i>
                        Send another reset email
                    </a>
                </div>
            </div>
        </div>

        <div class="text-center">
            <p class="text-sm text-text-secondary">
                Remember your password?
                <a href="{% url 'users:login' %}" class="text-primary hover:text-primary-hover font-medium transition duration-150">
                    Sign in here
                </a>
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-redirect to login after 5 minutes
    setTimeout(function() {
        if (confirm('Would you like to return to the login page?')) {
            window.location.href = "{% url 'users:login' %}";
        }
    }, 300000); // 5 minutes
});
</script>
{% endblock %}
