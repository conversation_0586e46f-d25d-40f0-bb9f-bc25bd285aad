{% extends 'base.html' %}

{% block title %}
  Profile - PDFlex
{% endblock %}

{% block content %}
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Profile Card -->
      <div class="lg:col-span-1">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="text-center">
            {% if profile_user.profile.avatar %}
              <img src="{{ profile_user.profile.avatar.url }}" alt="Avatar" class="w-32 h-32 rounded-full mx-auto mb-4 object-cover border-4 border-gray-100" />
            {% else %}
              <div class="w-32 h-32 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
                <i class="fa-solid fa-user text-gray-400 text-4xl"></i>
              </div>
            {% endif %}

            <h2 class="text-2xl font-bold text-text-primary mb-2">{{ profile_user.get_display_name }}</h2>
            <p class="text-text-secondary mb-4">{{ profile_user.get_role_display_with_company|default:'PDFlex User' }}</p>

            {% if is_own_profile %}
              <div class="space-y-2">
                <a href="{% url 'users:profile_edit' %}" class="w-full bg-primary hover:bg-primary-hover text-white px-4 py-2 rounded-lg transition duration-150 inline-flex items-center justify-center"><i class="fa-solid fa-edit mr-2"></i> Edit Profile</a>
                <a href="{% url 'users:account_edit' %}" class="w-full border border-gray-300 text-text-primary px-4 py-2 rounded-lg hover:bg-gray-50 transition duration-150 inline-flex items-center justify-center"><i class="fa-solid fa-cog mr-2"></i> Account Settings</a>
              </div>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Profile Information -->
      <div class="lg:col-span-2">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
          <div class="p-6 border-b border-gray-200">
            <h3 class="text-xl font-semibold text-text-primary flex items-center">
              <i class="fa-solid fa-user-circle mr-2"></i>
              Profile Information
            </h3>
          </div>
          <div class="p-6">
            <div class="space-y-6">
              <!-- Email -->
              <div class="flex items-center justify-between py-3 border-b border-gray-100">
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fa-solid fa-envelope text-blue-600"></i>
                  </div>
                  <div>
                    <p class="font-medium text-text-primary">Email</p>
                    <p class="text-sm text-text-secondary">{{ profile_user.email }}</p>
                  </div>
                </div>
                <div>
                  {% if profile_user.email_verified %}
                    <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full flex items-center"><i class="fa-solid fa-check-circle mr-1"></i> Verified</span>
                  {% else %}
                    <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full flex items-center"><i class="fa-solid fa-exclamation-circle mr-1"></i> Not Verified</span>
                  {% endif %}
                </div>
              </div>

              {% if profile_user.profile.bio %}
                <!-- Bio -->
                <div class="flex items-start space-x-3 py-3 border-b border-gray-100">
                  <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fa-solid fa-quote-left text-purple-600"></i>
                  </div>
                  <div>
                    <p class="font-medium text-text-primary">Bio</p>
                    <p class="text-sm text-text-secondary mt-1">{{ profile_user.profile.bio }}</p>
                  </div>
                </div>
              {% endif %}

              {% if profile_user.profile.phone %}
                <!-- Phone -->
                <div class="flex items-center space-x-3 py-3 border-b border-gray-100">
                  <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fa-solid fa-phone text-green-600"></i>
                  </div>
                  <div>
                    <p class="font-medium text-text-primary">Phone</p>
                    <p class="text-sm text-text-secondary">{{ profile_user.profile.phone }}</p>
                  </div>
                </div>
              {% endif %}

              <!-- Timezone -->
              <div class="flex items-center space-x-3 py-3 border-b border-gray-100">
                <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                  <i class="fa-solid fa-clock text-orange-600"></i>
                </div>
                <div>
                  <p class="font-medium text-text-primary">Timezone</p>
                  <p class="text-sm text-text-secondary">{{ profile_user.profile.timezone|default:'UTC' }}</p>
                </div>
              </div>

              <!-- Member Since -->
              <div class="flex items-center space-x-3 py-3 border-b border-gray-100">
                <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                  <i class="fa-solid fa-calendar text-indigo-600"></i>
                </div>
                <div>
                  <p class="font-medium text-text-primary">Member Since</p>
                  <p class="text-sm text-text-secondary">{{ profile_user.date_joined|date:'F d, Y' }}</p>
                </div>
              </div>

              {% if profile_user.profile.website or profile_user.profile.linkedin or profile_user.profile.github %}
                <!-- Social Links -->
                <div class="py-3">
                  <p class="font-medium text-text-primary mb-3">Social Links</p>
                  <div class="flex flex-wrap gap-2">
                    {% if profile_user.profile.website %}
                      <a href="{{ profile_user.profile.website }}" target="_blank" class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm text-text-primary hover:bg-gray-50 transition duration-150"><i class="fa-solid fa-globe mr-2"></i> Website</a>
                    {% endif %}
                    {% if profile_user.profile.linkedin %}
                      <a href="{{ profile_user.profile.linkedin }}" target="_blank" class="inline-flex items-center px-3 py-2 border border-blue-300 text-blue-700 rounded-lg text-sm hover:bg-blue-50 transition duration-150"><i class="fa-brands fa-linkedin mr-2"></i> LinkedIn</a>
                    {% endif %}
                    {% if profile_user.profile.github %}
                      <a href="{{ profile_user.profile.github }}" target="_blank" class="inline-flex items-center px-3 py-2 border border-gray-800 text-gray-800 rounded-lg text-sm hover:bg-gray-50 transition duration-150"><i class="fa-brands fa-github mr-2"></i> GitHub</a>
                    {% endif %}
                  </div>
                </div>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock %}
