{% extends 'base.html' %}

{% block title %}PDFlex - Universal Form-to-PDF Generator{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="bg-gradient-to-r from-primary to-primary-hover text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">
                Transform Forms into 
                <span class="text-blue-200">Professional PDFs</span>
            </h1>
            <p class="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">
                Create beautiful forms, generate stunning PDFs, and export production-ready code in Python, JavaScript, and PHP.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{% url 'users:register' %}" class="bg-white text-primary px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition duration-150">
                    Get Started Free
                </a>
                <a href="#demo" class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-primary transition duration-150">
                    Watch Demo
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-text-primary mb-4">
                Everything you need to create amazing PDFs
            </h2>
            <p class="text-xl text-text-secondary max-w-2xl mx-auto">
                From visual form builders to multi-language code generation, PDFlex has all the tools you need.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Feature 1 -->
            <div class="text-center p-6">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fa-solid fa-magic-wand-sparkles text-blue-600 text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-text-primary mb-3">Visual Form Builder</h3>
                <p class="text-text-secondary">
                    Drag and drop interface to create complex forms in minutes. No coding required.
                </p>
            </div>
            
            <!-- Feature 2 -->
            <div class="text-center p-6">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fa-solid fa-file-pdf text-green-600 text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-text-primary mb-3">PDF Generation</h3>
                <p class="text-text-secondary">
                    Generate professional PDFs with custom layouts, styling, and branding.
                </p>
            </div>
            
            <!-- Feature 3 -->
            <div class="text-center p-6">
                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fa-solid fa-code text-purple-600 text-2xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-text-primary mb-3">Code Export</h3>
                <p class="text-text-secondary">
                    Export production-ready code in Python, JavaScript, PHP with multiple library options.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- How It Works -->
<section class="py-20 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-text-primary mb-4">
                How PDFlex Works
            </h2>
            <p class="text-xl text-text-secondary">
                Three simple steps to transform your forms into professional PDFs
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Step 1 -->
            <div class="text-center">
                <div class="w-12 h-12 bg-primary text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                    1
                </div>
                <h3 class="text-xl font-semibold text-text-primary mb-3">Create Your Form</h3>
                <p class="text-text-secondary mb-4">
                    Use our intuitive drag-and-drop builder to create forms with text fields, dropdowns, checkboxes, and more.
                </p>
                <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
                    <div class="h-32 bg-gray-100 rounded flex items-center justify-center">
                        <i class="fa-solid fa-mouse-pointer text-gray-400 text-2xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- Step 2 -->
            <div class="text-center">
                <div class="w-12 h-12 bg-primary text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                    2
                </div>
                <h3 class="text-xl font-semibold text-text-primary mb-3">Design PDF Template</h3>
                <p class="text-text-secondary mb-4">
                    Map form fields to PDF elements and customize the layout, fonts, colors, and styling.
                </p>
                <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
                    <div class="h-32 bg-gray-100 rounded flex items-center justify-center">
                        <i class="fa-solid fa-file-pdf text-gray-400 text-2xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- Step 3 -->
            <div class="text-center">
                <div class="w-12 h-12 bg-primary text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                    3
                </div>
                <h3 class="text-xl font-semibold text-text-primary mb-3">Export & Deploy</h3>
                <p class="text-text-secondary mb-4">
                    Generate production-ready code and deploy to your application or use our hosted solution.
                </p>
                <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
                    <div class="h-32 bg-gray-100 rounded flex items-center justify-center">
                        <i class="fa-solid fa-rocket text-gray-400 text-2xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Supported Libraries -->
<section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-text-primary mb-4">
                Multiple Languages & Libraries
            </h2>
            <p class="text-xl text-text-secondary">
                Generate code for your preferred technology stack
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Python -->
            <div class="bg-blue-50 rounded-lg p-6 text-center">
                <i class="fa-brands fa-python text-blue-600 text-4xl mb-4"></i>
                <h3 class="text-xl font-semibold text-text-primary mb-3">Python</h3>
                <div class="space-y-2">
                    <span class="inline-block bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">ReportLab</span>
                    <span class="inline-block bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">FPDF2</span>
                    <span class="inline-block bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">WeasyPrint</span>
                </div>
            </div>
            
            <!-- JavaScript -->
            <div class="bg-yellow-50 rounded-lg p-6 text-center">
                <i class="fa-brands fa-js text-yellow-600 text-4xl mb-4"></i>
                <h3 class="text-xl font-semibold text-text-primary mb-3">JavaScript</h3>
                <div class="space-y-2">
                    <span class="inline-block bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm">jsPDF</span>
                    <span class="inline-block bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm">PDF-lib</span>
                    <span class="inline-block bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm">Puppeteer</span>
                </div>
            </div>
            
            <!-- PHP -->
            <div class="bg-purple-50 rounded-lg p-6 text-center">
                <i class="fa-brands fa-php text-purple-600 text-4xl mb-4"></i>
                <h3 class="text-xl font-semibold text-text-primary mb-3">PHP</h3>
                <div class="space-y-2">
                    <span class="inline-block bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm">TCPDF</span>
                    <span class="inline-block bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm">FPDF</span>
                    <span class="inline-block bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm">DomPDF</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-20 bg-gradient-to-r from-primary to-primary-hover text-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold mb-4">
            Ready to transform your forms?
        </h2>
        <p class="text-xl mb-8 text-blue-100">
            Join thousands of developers who trust PDFlex for their PDF generation needs.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="{% url 'users:register' %}" class="bg-white text-primary px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition duration-150">
                Start Free Trial
            </a>
            <a href="{% url 'api:documentation' %}" class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-primary transition duration-150">
                View Documentation
            </a>
        </div>
        
        <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
                <div class="text-3xl font-bold mb-2">10,000+</div>
                <div class="text-blue-200">Forms Created</div>
            </div>
            <div>
                <div class="text-3xl font-bold mb-2">50,000+</div>
                <div class="text-blue-200">PDFs Generated</div>
            </div>
            <div>
                <div class="text-3xl font-bold mb-2">99.9%</div>
                <div class="text-blue-200">Uptime</div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    // Smooth scrolling for anchor links
    document.addEventListener('DOMContentLoaded', function() {
        const links = document.querySelectorAll('a[href^="#"]');
        
        links.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // Add animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in');
                }
            });
        }, observerOptions);
        
        // Observe all sections
        document.querySelectorAll('section').forEach(section => {
            observer.observe(section);
        });
    });
</script>

<style>
    .animate-fade-in {
        animation: fadeIn 0.6s ease-in-out;
    }
    
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}
