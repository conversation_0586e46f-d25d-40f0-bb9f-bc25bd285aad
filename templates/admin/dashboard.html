{% extends 'base.html' %}

{% block title %}Admin Dashboard - PDFlex{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-text-primary">Admin Dashboard</h1>
                <p class="mt-2 text-text-secondary">System overview and management</p>
            </div>
            <div class="flex items-center space-x-3">
                <button class="bg-primary hover:bg-primary-hover text-white px-4 py-2 rounded-lg transition duration-150">
                    <i class="fa-solid fa-sync mr-2"></i>
                    Refresh Data
                </button>
            </div>
        </div>
    </div>

    <!-- System Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fa-solid fa-users text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-bold text-text-primary">{{ stats.total_users|default:0 }}</p>
                    <p class="text-text-secondary text-sm">Total Users</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fa-solid fa-file-text text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-bold text-text-primary">{{ stats.total_forms|default:0 }}</p>
                    <p class="text-text-secondary text-sm">Total Forms</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fa-solid fa-file-pdf text-purple-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-bold text-text-primary">{{ stats.total_templates|default:0 }}</p>
                    <p class="text-text-secondary text-sm">PDF Templates</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <i class="fa-solid fa-server text-orange-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-bold text-text-primary">{{ stats.system_health|default:"Good" }}</p>
                    <p class="text-text-secondary text-sm">System Status</p>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Recent Users -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-semibold text-text-primary">Recent Users</h2>
                    <a href="#" class="text-primary hover:text-primary-hover text-sm">View all</a>
                </div>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    {% for user in recent_users|default:""|slice:":5" %}
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                                <i class="fa-solid fa-user text-gray-600"></i>
                            </div>
                            <div>
                                <h3 class="font-medium text-text-primary">{{ user.get_display_name }}</h3>
                                <p class="text-sm text-text-secondary">{{ user.email }}</p>
                            </div>
                        </div>
                        <div class="text-sm text-text-secondary">
                            {{ user.date_joined|timesince }} ago
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-8">
                        <i class="fa-solid fa-users text-gray-400 text-3xl mb-3"></i>
                        <p class="text-text-secondary">No users found</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- System Logs -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-semibold text-text-primary">System Logs</h2>
                    <a href="#" class="text-primary hover:text-primary-hover text-sm">View all</a>
                </div>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    {% for log in system_logs|default:""|slice:":5" %}
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-{% if log.level == 'ERROR' %}red{% elif log.level == 'WARNING' %}yellow{% else %}blue{% endif %}-100 rounded-full flex items-center justify-center">
                            <i class="fa-solid fa-{% if log.level == 'ERROR' %}exclamation-triangle{% elif log.level == 'WARNING' %}exclamation-circle{% else %}info-circle{% endif %} text-{% if log.level == 'ERROR' %}red{% elif log.level == 'WARNING' %}yellow{% else %}blue{% endif %}-600 text-sm"></i>
                        </div>
                        <div class="flex-1">
                            <p class="text-text-primary text-sm">{{ log.message }}</p>
                            <p class="text-xs text-text-secondary">{{ log.timestamp|timesince }} ago</p>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-8">
                        <i class="fa-solid fa-file-text text-gray-400 text-3xl mb-3"></i>
                        <p class="text-text-secondary">No recent logs</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Management Actions -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mt-8">
        <div class="p-6 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-text-primary">Management Actions</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <a href="#" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-150">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="fa-solid fa-users-cog text-blue-600"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-text-primary">User Management</h3>
                        <p class="text-sm text-text-secondary">Manage user accounts</p>
                    </div>
                </a>

                <a href="#" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-150">
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="fa-solid fa-cog text-green-600"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-text-primary">System Settings</h3>
                        <p class="text-sm text-text-secondary">Configure system options</p>
                    </div>
                </a>

                <a href="#" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-150">
                    <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="fa-solid fa-chart-bar text-purple-600"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-text-primary">Analytics</h3>
                        <p class="text-sm text-text-secondary">View system analytics</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Admin dashboard functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-refresh data every 30 seconds
        setInterval(function() {
            // Add auto-refresh logic here
            console.log('Auto-refreshing dashboard data...');
        }, 30000);
    });
</script>
{% endblock %}
