<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Error - PDFlex</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- FontAwesome -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
    <!-- Inter Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap">
    
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: "#4f46e5",
                        "primary-hover": "#4338ca",
                        "text-primary": "#111827",
                        "text-secondary": "#6b7280"
                    },
                    fontFamily: {
                        sans: ["Inter", "sans-serif"]
                    }
                }
            }
        };
    </script>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full text-center">
            <div class="mb-8">
                <div class="mx-auto h-32 w-32 flex items-center justify-center rounded-full bg-red-100 mb-6">
                    <i class="fa-solid fa-exclamation-triangle text-red-500 text-5xl"></i>
                </div>
                <h1 class="text-6xl font-bold text-text-primary mb-4">500</h1>
                <h2 class="text-2xl font-semibold text-text-primary mb-4">Server Error</h2>
                <p class="text-text-secondary mb-8">
                    Something went wrong on our end. We're working to fix this issue. Please try again in a few minutes.
                </p>
            </div>
            
            <div class="space-y-4">
                <button onclick="window.location.reload()" class="w-full bg-primary hover:bg-primary-hover text-white px-6 py-3 rounded-lg transition duration-150 inline-flex items-center justify-center">
                    <i class="fa-solid fa-refresh mr-2"></i>
                    Try Again
                </button>
                
                <a href="/" class="w-full border border-gray-300 text-text-primary px-6 py-3 rounded-lg hover:bg-gray-50 transition duration-150 inline-flex items-center justify-center">
                    <i class="fa-solid fa-home mr-2"></i>
                    Go Home
                </a>
            </div>
            
            <div class="mt-8 pt-8 border-t border-gray-200">
                <p class="text-sm text-text-secondary mb-4">If the problem persists, please contact support:</p>
                <div class="space-y-2">
                    <a href="mailto:<EMAIL>" class="block text-primary hover:text-primary-hover text-sm">
                        <i class="fa-solid fa-envelope mr-1"></i>
                        <EMAIL>
                    </a>
                    <a href="#" class="block text-primary hover:text-primary-hover text-sm">
                        <i class="fa-solid fa-life-ring mr-1"></i>
                        Help Center
                    </a>
                    <a href="#" class="block text-primary hover:text-primary-hover text-sm">
                        <i class="fa-solid fa-comments mr-1"></i>
                        Live Chat
                    </a>
                </div>
            </div>
            
            <div class="mt-8 text-xs text-text-secondary">
                Error ID: <span id="error-id"></span>
            </div>
        </div>
    </div>

    <script>
        // Generate a random error ID for tracking
        document.getElementById('error-id').textContent = 'ERR-' + Math.random().toString(36).substr(2, 9).toUpperCase();
        
        // Track 500 errors for monitoring
        console.error('500 Server Error occurred at:', new Date().toISOString());
    </script>
</body>
</html>
