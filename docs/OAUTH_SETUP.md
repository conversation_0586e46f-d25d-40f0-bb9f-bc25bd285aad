# OAuth Setup Guide for PDFlex

This guide explains how to set up Google and GitHub OAuth authentication for the PDFlex application.

## Quick Fix (Development)

The application now automatically handles OAuth configuration:

- **OAuth buttons only appear when properly configured** - No more errors!
- **Graceful fallback** - If OAuth isn't set up, users can still login with email/password
- **Smart detection** - The system automatically detects valid OAuth credentials

If you want to completely remove OAuth applications:

```bash
python manage.py setup_dev_oauth --disable-oauth
```

## Setting Up Google OAuth

### 1. Create Google OAuth Application

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API:
   - Go to "APIs & Services" > "Library"
   - Search for "Google+ API" and enable it
4. Create OAuth 2.0 credentials:
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"
   - Choose "Web application"
   - Add authorized redirect URIs:
     - `http://localhost:8000/accounts/google/login/callback/` (development)
     - `https://yourdomain.com/accounts/google/login/callback/` (production)

### 2. Configure Google OAuth in PDFlex

```bash
python manage.py setup_dev_oauth \
  --google-client-id "your-google-client-id" \
  --google-client-secret "your-google-client-secret"
```

## Setting Up GitHub OAuth

### 1. Create GitHub OAuth Application

1. Go to GitHub Settings > Developer settings > OAuth Apps
2. Click "New OAuth App"
3. Fill in the details:
   - **Application name**: PDFlex
   - **Homepage URL**: `http://localhost:8000` (development) or your domain
   - **Authorization callback URL**: `http://localhost:8000/accounts/github/login/callback/`

### 2. Configure GitHub OAuth in PDFlex

```bash
python manage.py setup_dev_oauth \
  --github-client-id "your-github-client-id" \
  --github-client-secret "your-github-client-secret"
```

## Setting Up Both Providers

You can set up both providers in one command:

```bash
python manage.py setup_dev_oauth \
  --google-client-id "your-google-client-id" \
  --google-client-secret "your-google-client-secret" \
  --github-client-id "your-github-client-id" \
  --github-client-secret "your-github-client-secret"
```

## Manual Setup via Django Admin

Alternatively, you can set up OAuth applications manually:

1. Start the development server: `python manage.py runserver`
2. Go to `http://localhost:8000/admin/`
3. Navigate to **Sites** > **Sites**
4. Update the default site:
   - Domain: `localhost:8000` (development) or your domain
   - Display name: `PDFlex`

5. Navigate to **Social Applications** > **Social applications**
6. Add Google application:
   - Provider: `Google`
   - Name: `Google`
   - Client id: `[your-google-client-id]`
   - Secret key: `[your-google-client-secret]`
   - Sites: Select your site

7. Add GitHub application:
   - Provider: `GitHub`
   - Name: `GitHub`
   - Client id: `[your-github-client-id]`
   - Secret key: `[your-github-client-secret]`
   - Sites: Select your site

## Environment Variables (Production)

For production, you can also use environment variables:

```bash
# .env file
GOOGLE_OAUTH_CLIENT_ID=your-google-client-id
GOOGLE_OAUTH_CLIENT_SECRET=your-google-client-secret
GITHUB_OAUTH_CLIENT_ID=your-github-client-id
GITHUB_OAUTH_CLIENT_SECRET=your-github-client-secret
```

Then update your settings to use these variables.

## Troubleshooting

### OAuth Error: "DoesNotExist at /users/auth/google/login/"

This error occurs when the OAuth application is not configured. Solutions:

1. **Quick fix**: Disable OAuth temporarily
   ```bash
   python manage.py setup_dev_oauth --disable-oauth
   ```

2. **Proper fix**: Set up OAuth credentials using the commands above

### OAuth Buttons Not Showing

The login/registration templates are configured to only show OAuth buttons when providers are properly configured. If you don't see the buttons, it means no OAuth applications are set up.

### Redirect URI Mismatch

Make sure your OAuth application's redirect URIs match exactly:
- Development: `http://localhost:8000/accounts/[provider]/login/callback/`
- Production: `https://yourdomain.com/accounts/[provider]/login/callback/`

### Testing OAuth

1. Set up OAuth credentials using the management command
2. Restart the development server
3. Go to the login page - you should see OAuth buttons
4. Click on a provider button to test the OAuth flow

## Security Notes

- Never commit OAuth secrets to version control
- Use environment variables for production
- Regularly rotate OAuth secrets
- Use HTTPS in production for OAuth callbacks
- Restrict OAuth application domains in production

## Support

If you encounter issues:

1. Check the Django logs for detailed error messages
2. Verify your OAuth application settings in Google/GitHub
3. Ensure redirect URIs match exactly
4. Test with the management command first before manual setup
