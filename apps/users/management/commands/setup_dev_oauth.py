"""
Management command to set up OAuth applications for development.
This creates working OAuth apps with development-friendly settings.
"""

from django.core.management.base import BaseCommand
from django.contrib.sites.models import Site
from allauth.socialaccount.models import SocialApp


class Command(BaseCommand):
    help = 'Set up OAuth applications for development environment'

    def add_arguments(self, parser):
        parser.add_argument(
            '--google-client-id',
            type=str,
            help='Google OAuth Client ID',
        )
        parser.add_argument(
            '--google-client-secret',
            type=str,
            help='Google OAuth Client Secret',
        )
        parser.add_argument(
            '--github-client-id',
            type=str,
            help='GitHub OAuth Client ID',
        )
        parser.add_argument(
            '--github-client-secret',
            type=str,
            help='GitHub OAuth Client Secret',
        )
        parser.add_argument(
            '--disable-oauth',
            action='store_true',
            help='Disable OAuth by removing social apps',
        )

    def handle(self, *args, **options):
        if options['disable_oauth']:
            self.disable_oauth()
            return

        self.stdout.write('Setting up OAuth applications for development...')
        
        # Get or create the default site
        site, created = Site.objects.get_or_create(
            pk=1,
            defaults={
                'domain': 'localhost:8000',
                'name': 'PDFlex Development'
            }
        )
        
        if created:
            self.stdout.write('Created default site')
        else:
            # Update site for development
            site.domain = 'localhost:8000'
            site.name = 'PDFlex Development'
            site.save()
            self.stdout.write('Updated default site for development')

        # Set up Google OAuth
        if options['google_client_id'] and options['google_client_secret']:
            self.setup_google_oauth(site, options['google_client_id'], options['google_client_secret'])
        else:
            self.stdout.write(
                self.style.WARNING(
                    'No Google OAuth credentials provided. '
                    'Use --google-client-id and --google-client-secret to set up Google OAuth.'
                )
            )

        # Set up GitHub OAuth
        if options['github_client_id'] and options['github_client_secret']:
            self.setup_github_oauth(site, options['github_client_id'], options['github_client_secret'])
        else:
            self.stdout.write(
                self.style.WARNING(
                    'No GitHub OAuth credentials provided. '
                    'Use --github-client-id and --github-client-secret to set up GitHub OAuth.'
                )
            )

        self.stdout.write(
            self.style.SUCCESS('OAuth setup completed!')
        )

    def setup_google_oauth(self, site, client_id, client_secret):
        """Set up Google OAuth application."""
        google_app, created = SocialApp.objects.update_or_create(
            provider='google',
            name='Google',
            defaults={
                'client_id': client_id,
                'secret': client_secret,
            }
        )
        
        google_app.sites.add(site)
        
        if created:
            self.stdout.write(self.style.SUCCESS('Created Google OAuth app'))
        else:
            self.stdout.write(self.style.SUCCESS('Updated Google OAuth app'))

    def setup_github_oauth(self, site, client_id, client_secret):
        """Set up GitHub OAuth application."""
        github_app, created = SocialApp.objects.update_or_create(
            provider='github',
            name='GitHub',
            defaults={
                'client_id': client_id,
                'secret': client_secret,
            }
        )
        
        github_app.sites.add(site)
        
        if created:
            self.stdout.write(self.style.SUCCESS('Created GitHub OAuth app'))
        else:
            self.stdout.write(self.style.SUCCESS('Updated GitHub OAuth app'))

    def disable_oauth(self):
        """Disable OAuth by removing social applications."""
        self.stdout.write('Disabling OAuth applications...')
        
        deleted_count = 0
        
        # Remove Google app
        google_apps = SocialApp.objects.filter(provider='google')
        count = google_apps.count()
        google_apps.delete()
        deleted_count += count
        
        # Remove GitHub app
        github_apps = SocialApp.objects.filter(provider='github')
        count = github_apps.count()
        github_apps.delete()
        deleted_count += count
        
        self.stdout.write(
            self.style.SUCCESS(f'Disabled OAuth - removed {deleted_count} social applications')
        )
        self.stdout.write(
            self.style.WARNING(
                'OAuth login buttons will now show an error. '
                'Run this command with OAuth credentials to re-enable.'
            )
        )
