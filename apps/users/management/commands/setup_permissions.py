from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.contrib.sites.models import Site
from allauth.socialaccount.models import SocialApp
from apps.users.models import User, UserProfile


class Command(BaseCommand):
    help = 'Set up user groups and permissions for PDFLEX'

    def handle(self, *args, **options):
        self.stdout.write('Setting up user groups and permissions...')

        # Create groups
        self.create_groups()

        # Set up permissions
        self.setup_permissions()

        # Set up social applications
        self.setup_social_apps()

        self.stdout.write(
            self.style.SUCCESS('Successfully set up user groups, permissions, and social apps!')
        )

    def create_groups(self):
        """Create user groups based on roles."""
        groups_data = {
            'Administrators': {
                'description': 'Full system access',
                'permissions': [
                    'add_user', 'change_user', 'delete_user', 'view_user',
                    'add_userprofile', 'change_userprofile', 'delete_userprofile', 'view_userprofile',
                    # Add more permissions as needed for other models
                ]
            },
            'Managers': {
                'description': 'Management access with some restrictions',
                'permissions': [
                    'add_user', 'change_user', 'view_user',
                    'add_userprofile', 'change_userprofile', 'view_userprofile',
                    # Add more permissions as needed
                ]
            },
            'Users': {
                'description': 'Standard user access',
                'permissions': [
                    'change_user', 'view_user',
                    'change_userprofile', 'view_userprofile',
                    # Add more permissions as needed
                ]
            },
            'Viewers': {
                'description': 'Read-only access',
                'permissions': [
                    'view_user', 'view_userprofile',
                    # Add more permissions as needed
                ]
            }
        }
        
        for group_name, group_info in groups_data.items():
            group, created = Group.objects.get_or_create(name=group_name)
            if created:
                self.stdout.write(f'Created group: {group_name}')
            else:
                self.stdout.write(f'Group already exists: {group_name}')
            
            # Clear existing permissions
            group.permissions.clear()
            
            # Add permissions to group
            for perm_codename in group_info['permissions']:
                try:
                    permission = Permission.objects.get(codename=perm_codename)
                    group.permissions.add(permission)
                except Permission.DoesNotExist:
                    self.stdout.write(
                        self.style.WARNING(f'Permission not found: {perm_codename}')
                    )

    def setup_permissions(self):
        """Set up custom permissions."""
        # Get content types
        user_ct = ContentType.objects.get_for_model(User)
        profile_ct = ContentType.objects.get_for_model(UserProfile)
        
        # Custom permissions for User model
        custom_user_permissions = [
            ('can_view_all_users', 'Can view all users'),
            ('can_manage_user_roles', 'Can manage user roles'),
            ('can_verify_users', 'Can verify user accounts'),
            ('can_deactivate_users', 'Can deactivate user accounts'),
        ]
        
        for codename, name in custom_user_permissions:
            permission, created = Permission.objects.get_or_create(
                codename=codename,
                name=name,
                content_type=user_ct,
            )
            if created:
                self.stdout.write(f'Created permission: {name}')
        
        # Custom permissions for UserProfile model
        custom_profile_permissions = [
            ('can_view_all_profiles', 'Can view all user profiles'),
            ('can_moderate_profiles', 'Can moderate user profiles'),
        ]
        
        for codename, name in custom_profile_permissions:
            permission, created = Permission.objects.get_or_create(
                codename=codename,
                name=name,
                content_type=profile_ct,
            )
            if created:
                self.stdout.write(f'Created permission: {name}')
        
        # Add custom permissions to groups
        self.add_custom_permissions_to_groups()

    def add_custom_permissions_to_groups(self):
        """Add custom permissions to appropriate groups."""
        try:
            # Administrators get all custom permissions
            admin_group = Group.objects.get(name='Administrators')
            admin_permissions = [
                'can_view_all_users', 'can_manage_user_roles',
                'can_verify_users', 'can_deactivate_users',
                'can_view_all_profiles', 'can_moderate_profiles'
            ]
            
            for perm_codename in admin_permissions:
                try:
                    permission = Permission.objects.get(codename=perm_codename)
                    admin_group.permissions.add(permission)
                except Permission.DoesNotExist:
                    pass
            
            # Managers get some custom permissions
            manager_group = Group.objects.get(name='Managers')
            manager_permissions = [
                'can_view_all_users', 'can_verify_users',
                'can_view_all_profiles'
            ]
            
            for perm_codename in manager_permissions:
                try:
                    permission = Permission.objects.get(codename=perm_codename)
                    manager_group.permissions.add(permission)
                except Permission.DoesNotExist:
                    pass
                    
        except Group.DoesNotExist:
            self.stdout.write(
                self.style.ERROR('Some groups do not exist. Please run this command again.')
            )

    def setup_social_apps(self):
        """Set up social applications for OAuth providers."""
        self.stdout.write('Setting up social applications...')

        # Get or create the default site
        site, created = Site.objects.get_or_create(
            pk=1,
            defaults={
                'domain': 'localhost:8000',
                'name': 'PDFlex Development'
            }
        )

        if created:
            self.stdout.write('Created default site')

        # Set up Google OAuth app (placeholder)
        google_app, created = SocialApp.objects.get_or_create(
            provider='google',
            name='Google',
            defaults={
                'client_id': 'your-google-client-id',
                'secret': 'your-google-client-secret',
            }
        )

        if created:
            google_app.sites.add(site)
            self.stdout.write(
                self.style.WARNING(
                    'Created Google OAuth app with placeholder credentials. '
                    'Please update with real credentials in Django admin.'
                )
            )
        else:
            self.stdout.write('Google OAuth app already exists')

        # Set up GitHub OAuth app (placeholder)
        github_app, created = SocialApp.objects.get_or_create(
            provider='github',
            name='GitHub',
            defaults={
                'client_id': 'your-github-client-id',
                'secret': 'your-github-client-secret',
            }
        )

        if created:
            github_app.sites.add(site)
            self.stdout.write(
                self.style.WARNING(
                    'Created GitHub OAuth app with placeholder credentials. '
                    'Please update with real credentials in Django admin.'
                )
            )
        else:
            self.stdout.write('GitHub OAuth app already exists')
