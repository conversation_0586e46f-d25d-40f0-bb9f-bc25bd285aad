"""
Custom template tags for OAuth functionality.
"""

from django import template
from django.contrib.sites.models import Site
from allauth.socialaccount.models import SocialApp
from allauth.socialaccount.templatetags.socialaccount import get_providers

register = template.Library()


@register.simple_tag
def get_configured_providers():
    """
    Get only properly configured social providers.
    Returns a list of provider IDs that have valid SocialApp configurations.
    """
    try:
        current_site = Site.objects.get_current()
        configured_providers = []
        
        # Get all available providers
        all_providers = get_providers()
        
        for provider in all_providers:
            # Check if there's a SocialApp for this provider
            try:
                social_app = SocialApp.objects.get(
                    provider=provider.id,
                    sites=current_site
                )
                # Check if the app has valid credentials
                if social_app.client_id and social_app.secret:
                    # Exclude placeholder credentials
                    if (social_app.client_id != 'your-google-client-id' and 
                        social_app.client_id != 'your-github-client-id' and
                        social_app.secret != 'your-google-client-secret' and
                        social_app.secret != 'your-github-client-secret'):
                        configured_providers.append(provider.id)
            except SocialApp.DoesNotExist:
                continue
                
        return configured_providers
    except Exception:
        return []


@register.simple_tag
def is_provider_configured(provider_id):
    """
    Check if a specific provider is properly configured.
    """
    try:
        current_site = Site.objects.get_current()
        social_app = SocialApp.objects.get(
            provider=provider_id,
            sites=current_site
        )
        # Check if the app has valid credentials (not placeholders)
        if (social_app.client_id and social_app.secret and
            social_app.client_id not in ['your-google-client-id', 'your-github-client-id'] and
            social_app.secret not in ['your-google-client-secret', 'your-github-client-secret']):
            return True
    except (SocialApp.DoesNotExist, Site.DoesNotExist):
        pass
    return False


@register.inclusion_tag('users/partials/oauth_buttons.html')
def oauth_buttons(button_class="w-full flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition duration-150"):
    """
    Render OAuth buttons for configured providers.
    """
    configured_providers = get_configured_providers()
    return {
        'configured_providers': configured_providers,
        'button_class': button_class
    }
