"""
Views for code generator app
"""

from django.shortcuts import render, get_object_or_404
from django.http import HttpR<PERSON>ponse, Http404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.urls import reverse
import uuid


@login_required
def generator_view(request):
    """Code generator main view."""
    context = {
        'supported_languages': ['python', 'javascript', 'php'],
        'supported_libraries': {
            'python': ['reportlab', 'weasyprint', 'fpdf2'],
            'javascript': ['jspdf', 'pdfkit', 'puppeteer'],
            'php': ['tcpdf', 'fpdf', 'dompdf']
        }
    }
    return render(request, 'code_generator/generator.html', context)


@login_required
def preview_view(request):
    """Code preview view."""
    # Get parameters from request
    template_name = request.GET.get('template_name', 'Contact Form')
    language = request.GET.get('language', 'python')
    library = request.GET.get('library', 'reportlab')
    
    context = {
        'template_name': template_name,
        'language': language,
        'library': library,
    }
    return render(request, 'code_generator/preview.html', context)


@login_required
def download_code(request, generated_code_id):
    """Download generated code."""
    # This would typically fetch the generated code from database
    # For now, return a placeholder response
    try:
        # Validate UUID
        uuid.UUID(str(generated_code_id))
        
        # In a real implementation, you would:
        # generated_code = get_object_or_404(GeneratedCode, id=generated_code_id, user=request.user)
        # return HttpResponse(generated_code.content, content_type='application/zip')
        
        return HttpResponse("Generated code download would be implemented here", 
                          content_type='text/plain')
    except ValueError:
        raise Http404("Invalid code ID")


@login_required
def preview_code(request, generated_code_id):
    """Preview generated code."""
    try:
        # Validate UUID
        uuid.UUID(str(generated_code_id))
        
        # In a real implementation, you would:
        # generated_code = get_object_or_404(GeneratedCode, id=generated_code_id, user=request.user)
        
        context = {
            'generated_code_id': generated_code_id,
            'template_name': 'Sample Template',
            'language': 'python',
            'library': 'reportlab',
        }
        return render(request, 'code_generator/preview.html', context)
    except ValueError:
        raise Http404("Invalid code ID")
