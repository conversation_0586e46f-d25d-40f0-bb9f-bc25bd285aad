"""
Views for webhooks app
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.http import HttpResponse, JsonResponse, Http404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
import uuid
import json


@login_required
def webhook_list(request):
    """List all webhooks for the user."""
    # In a real implementation, you would fetch webhooks from database
    # webhooks = Webhook.objects.filter(user=request.user)
    
    context = {
        'webhooks': [],  # Placeholder - would be actual webhook objects
    }
    return render(request, 'webhooks/configuration.html', context)


@login_required
def configuration_view(request):
    """Webhook configuration main view."""
    # In a real implementation, you would fetch webhooks from database
    # webhooks = Webhook.objects.filter(user=request.user)

    # Create a mock queryset-like object for template compatibility
    class MockWebhooks:
        def count(self):
            return 2

    context = {
        'webhooks': MockWebhooks(),  # Mock object with count method
        'webhook_events': [
            'form.submitted',
            'pdf.generated',
            'code.generated',
            'error.occurred'
        ]
    }
    return render(request, 'webhooks/configuration.html', context)


@login_required
@require_http_methods(["GET", "POST"])
def webhook_create(request):
    """Create a new webhook."""
    if request.method == 'POST':
        # In a real implementation, you would:
        # - Validate the form data
        # - Create a new Webhook object
        # - Save to database
        # - Redirect to webhook list
        
        messages.success(request, 'Webhook created successfully!')
        return redirect('webhooks:configuration')
    
    context = {
        'webhook_events': [
            'form.submitted',
            'pdf.generated',
            'code.generated',
            'error.occurred'
        ]
    }
    return render(request, 'webhooks/webhook_create.html', context)


@login_required
@require_http_methods(["GET", "POST"])
def webhook_edit(request, webhook_id):
    """Edit an existing webhook."""
    try:
        # Validate UUID
        uuid.UUID(str(webhook_id))
        
        # In a real implementation:
        # webhook = get_object_or_404(Webhook, id=webhook_id, user=request.user)
        
        if request.method == 'POST':
            # Update webhook logic would go here
            messages.success(request, 'Webhook updated successfully!')
            return redirect('webhooks:configuration')
        
        context = {
            'webhook_id': webhook_id,
            'webhook': {  # Placeholder webhook data
                'url': 'https://example.com/webhook',
                'events': ['form.submitted'],
                'is_active': True
            },
            'webhook_events': [
                'form.submitted',
                'pdf.generated',
                'code.generated',
                'error.occurred'
            ]
        }
        return render(request, 'webhooks/webhook_edit.html', context)
    except ValueError:
        raise Http404("Invalid webhook ID")


@login_required
@require_http_methods(["POST"])
def webhook_delete(request, webhook_id):
    """Delete a webhook."""
    try:
        # Validate UUID
        uuid.UUID(str(webhook_id))
        
        # In a real implementation:
        # webhook = get_object_or_404(Webhook, id=webhook_id, user=request.user)
        # webhook.delete()
        
        messages.success(request, 'Webhook deleted successfully!')
        return redirect('webhooks:configuration')
    except ValueError:
        raise Http404("Invalid webhook ID")


@login_required
@require_http_methods(["POST"])
def webhook_test(request, webhook_id):
    """Test a webhook."""
    try:
        # Validate UUID
        uuid.UUID(str(webhook_id))
        
        # In a real implementation:
        # webhook = get_object_or_404(Webhook, id=webhook_id, user=request.user)
        # result = webhook.test()
        
        return JsonResponse({
            'success': True,
            'message': 'Test webhook sent successfully!',
            'response_code': 200
        })
    except ValueError:
        return JsonResponse({
            'success': False,
            'message': 'Invalid webhook ID'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Error testing webhook: {str(e)}'
        }, status=500)
