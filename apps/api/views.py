from django.shortcuts import render
from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework.decorators import api_view


@api_view(['GET'])
def api_root(request):
    """API root endpoint"""
    return Response({
        'message': 'Welcome to PDFLEX API',
        'version': 'v1',
        'endpoints': {
            'forms': '/api/v1/forms/',
            'pdf-templates': '/api/v1/pdf-templates/',
        }
    })


def documentation_view(request):
    """API documentation view"""
    return render(request, 'api/documentation.html')


# Placeholder ViewSets - will be implemented later
# class FormViewSet(viewsets.ModelViewSet):
#     pass

# class PDFTemplateViewSet(viewsets.ModelViewSet):
#     pass
