"""
URL configuration for API app
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
# router.register(r'forms', views.FormViewSet)
# router.register(r'pdf-templates', views.PDFTemplateViewSet)

app_name = 'api'

urlpatterns = [
    path('', views.api_root, name='api_root'),
    path('', include(router.urls)),
    path('auth/', include('rest_framework.urls')),
    path('users/', include('apps.users.api_urls')),
    path('documentation/', views.documentation_view, name='documentation'),
]
